import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User, UserProfile } from '@/types/user'

export const useUserStore = defineStore('user', () => {
  const token = ref<string>('')
  const userInfo = ref<User | null>(null)
  const userProfile = ref<UserProfile | null>(null)

  const isLoggedIn = computed(() => !!token.value)

  function setToken(newToken: string) {
    token.value = newToken
    localStorage.setItem('token', newToken)
  }

  function setUserInfo(info: User) {
    userInfo.value = info
  }

  function setUserProfile(profile: UserProfile) {
    userProfile.value = profile
  }

  function logout() {
    token.value = ''
    userInfo.value = null
    userProfile.value = null
    localStorage.removeItem('token')
  }

  function initFromStorage() {
    const savedToken = localStorage.getItem('token')
    if (savedToken) {
      token.value = savedToken
    }
  }

  return {
    token,
    userInfo,
    userProfile,
    isLoggedIn,
    setToken,
    setUserInfo,
    setUserProfile,
    logout,
    initFromStorage
  }
})
