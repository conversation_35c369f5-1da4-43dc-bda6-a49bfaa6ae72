package com.healthdiet.test.controller;

import com.healthdiet.common.result.Result;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 测试控制器
 */
@RestController
@RequestMapping("/test")
public class TestController {

    @GetMapping("/hello")
    public Result<String> hello() {
        return Result.success("Hello, 健康饮食平台测试服务运行正常！");
    }
    
    @GetMapping("/health")
    public Result<String> health() {
        return Result.success("服务健康状态：正常");
    }
}
