# 健康饮食推荐平台

基于微服务架构与AIGC的健康饮食推荐平台

## 快速启动

### 方式一：一键启动（推荐）

```bash
# Windows环境
start-all-services.bat
```

### 方式二：分步启动

1. **启动基础设施服务**
```bash
docker-compose up -d
```

2. **编译后端项目**
```bash
cd health-backend
mvn clean compile
```

3. **启动微服务**
```bash
# 认证服务
cd auth-service && mvn spring-boot:run

# 用户服务
cd user-service && mvn spring-boot:run

# 网关服务
cd gateway-service && mvn spring-boot:run
```

4. **启动前端服务**
```bash
cd health-frontend
pnpm install
pnpm run dev
```

## 服务架构

### 基础设施服务
- **MySQL**: 3307端口 (用户名: root, 密码: mysql123)
- **Redis**: 6380端口
- **RabbitMQ**: 5672端口, 管理界面: 15672端口
- **Nacos**: 8848端口 (用户名/密码: nacos/nacos)

### 微服务
- **网关服务**: 8080端口
- **认证服务**: 8081端口
- **用户服务**: 8082端口
- **菜谱服务**: 8083端口
- **AIGC服务**: 8085端口

### 前端服务
- **Vue3应用**: 3001端口

## 访问地址

- **前端应用**: http://localhost:3001
- **API网关**: http://localhost:8080
- **Nacos控制台**: http://localhost:8848/nacos
- **RabbitMQ管理**: http://localhost:15672

## 停止服务

```bash
# Windows环境
stop-all-services.bat

# 或手动停止
docker-compose down
```

## 技术栈

### 后端
- Spring Boot 3.2.5
- Spring Cloud 2023.0.2
- Spring Cloud Alibaba 2023.0.1.0
- MyBatis-Plus 3.5.7
- MySQL 8.0
- Redis 7
- RabbitMQ 3.12

### 前端
- Vue 3
- TypeScript
- Element Plus
- Vite

## 开发说明

详细的开发文档请查看：
- [后端服务文档](./health-backend/README.md)
- [前端应用文档](./health-frontend/README.md)
- [需求规格说明书](./需求规格说明书.md)
