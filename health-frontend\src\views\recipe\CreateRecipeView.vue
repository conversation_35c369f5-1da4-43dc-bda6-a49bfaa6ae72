<template>
  <MainLayout>
    <div class="create-recipe-container">
      <el-card>
        <template #header>
          <h2>创建菜谱</h2>
        </template>
        
        <el-form
          ref="recipeFormRef"
          :model="recipeForm"
          :rules="recipeRules"
          label-width="100px"
        >
          <el-form-item label="菜谱名称" prop="title">
            <el-input v-model="recipeForm.title" placeholder="请输入菜谱名称" />
          </el-form-item>
          
          <el-form-item label="菜谱描述" prop="description">
            <el-input
              v-model="recipeForm.description"
              type="textarea"
              :rows="3"
              placeholder="请输入菜谱描述"
            />
          </el-form-item>
          
          <el-row :gutter="24">
            <el-col :span="8">
              <el-form-item label="烹饪时间" prop="cookingTime">
                <el-input-number
                  v-model="recipeForm.cookingTime"
                  :min="1"
                  :max="300"
                  placeholder="分钟"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="难度等级" prop="difficulty">
                <el-select v-model="recipeForm.difficulty" placeholder="选择难度" style="width: 100%">
                  <el-option label="简单" :value="1" />
                  <el-option label="中等" :value="2" />
                  <el-option label="困难" :value="3" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="份数" prop="servings">
                <el-input-number
                  v-model="recipeForm.servings"
                  :min="1"
                  :max="20"
                  placeholder="人份"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-form-item label="封面图片">
            <el-upload
              class="cover-uploader"
              :show-file-list="false"
              :on-success="handleCoverSuccess"
              :before-upload="beforeCoverUpload"
              action="/api/recipes/upload"
            >
              <img v-if="recipeForm.coverImageUrl" :src="recipeForm.coverImageUrl" class="cover-image" />
              <el-icon v-else class="cover-uploader-icon"><Plus /></el-icon>
            </el-upload>
          </el-form-item>
          
          <el-form-item label="食材清单" prop="ingredients">
            <div class="ingredients-section">
              <div
                v-for="(ingredient, index) in recipeForm.ingredients"
                :key="index"
                class="ingredient-item"
              >
                <el-input
                  v-model="ingredient.ingredientName"
                  placeholder="食材名称"
                  style="width: 200px; margin-right: 12px"
                />
                <el-input
                  v-model="ingredient.quantity"
                  placeholder="用量"
                  style="width: 150px; margin-right: 12px"
                />
                <el-button type="danger" @click="removeIngredient(index)" :icon="Delete" />
              </div>
              <el-button type="primary" @click="addIngredient" :icon="Plus">添加食材</el-button>
            </div>
          </el-form-item>
          
          <el-form-item label="制作步骤" prop="steps">
            <div class="steps-section">
              <div
                v-for="(step, index) in recipeForm.steps"
                :key="index"
                class="step-item"
              >
                <div class="step-number">{{ index + 1 }}</div>
                <div class="step-content">
                  <el-input
                    v-model="step.instruction"
                    type="textarea"
                    :rows="2"
                    placeholder="请输入制作步骤"
                    style="margin-bottom: 12px"
                  />
                  <el-upload
                    class="step-uploader"
                    :show-file-list="false"
                    :on-success="(response: any) => handleStepImageSuccess(response, index)"
                    :before-upload="beforeStepImageUpload"
                    action="/api/recipes/upload"
                  >
                    <img v-if="step.imageUrl" :src="step.imageUrl" class="step-image" />
                    <el-button v-else type="text">
                      <el-icon><Picture /></el-icon>
                      添加图片
                    </el-button>
                  </el-upload>
                </div>
                <el-button type="danger" @click="removeStep(index)" :icon="Delete" />
              </div>
              <el-button type="primary" @click="addStep" :icon="Plus">添加步骤</el-button>
            </div>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="handleSubmit" :loading="submitting">
              创建菜谱
            </el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
  </MainLayout>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, type FormInstance, type FormRules, type UploadProps } from 'element-plus'
import { Delete, Plus } from '@element-plus/icons-vue'
import MainLayout from '@/components/Layout/MainLayout.vue'
import { createRecipe } from '@/api/recipe'
import type { RecipeCreateRequest } from '@/types/recipe'

const router = useRouter()

const recipeFormRef = ref<FormInstance>()
const submitting = ref(false)

const recipeForm = reactive<RecipeCreateRequest>({
  title: '',
  description: '',
  coverImageUrl: '',
  cookingTime: 30,
  difficulty: 1,
  servings: 2,
  ingredients: [{ ingredientName: '', quantity: '' }],
  steps: [{ stepNumber: 1, instruction: '', imageUrl: '' }]
})

const recipeRules: FormRules = {
  title: [
    { required: true, message: '请输入菜谱名称', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入菜谱描述', trigger: 'blur' }
  ],
  cookingTime: [
    { required: true, message: '请输入烹饪时间', trigger: 'blur' }
  ],
  difficulty: [
    { required: true, message: '请选择难度等级', trigger: 'change' }
  ],
  servings: [
    { required: true, message: '请输入份数', trigger: 'blur' }
  ]
}

const addIngredient = () => {
  recipeForm.ingredients.push({ ingredientName: '', quantity: '' })
}

const removeIngredient = (index: number) => {
  if (recipeForm.ingredients.length > 1) {
    recipeForm.ingredients.splice(index, 1)
  }
}

const addStep = () => {
  recipeForm.steps.push({
    stepNumber: recipeForm.steps.length + 1,
    instruction: '',
    imageUrl: ''
  })
}

const removeStep = (index: number) => {
  if (recipeForm.steps.length > 1) {
    recipeForm.steps.splice(index, 1)
    // 重新编号
    recipeForm.steps.forEach((step, i) => {
      step.stepNumber = i + 1
    })
  }
}

const handleCoverSuccess: UploadProps['onSuccess'] = (response) => {
  recipeForm.coverImageUrl = response.data
  ElMessage.success('封面图片上传成功')
}

const beforeCoverUpload: UploadProps['beforeUpload'] = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

const handleStepImageSuccess = (response: any, index: number) => {
  recipeForm.steps[index].imageUrl = response.data
  ElMessage.success('步骤图片上传成功')
}

const beforeStepImageUpload = beforeCoverUpload

const handleSubmit = async () => {
  if (!recipeFormRef.value) return
  
  try {
    await recipeFormRef.value.validate()
    
    // 过滤空的食材和步骤
    const validIngredients = recipeForm.ingredients.filter(
      item => item.ingredientName.trim() && item.quantity.trim()
    )
    const validSteps = recipeForm.steps.filter(
      item => item.instruction.trim()
    )
    
    if (validIngredients.length === 0) {
      ElMessage.error('请至少添加一个食材')
      return
    }
    
    if (validSteps.length === 0) {
      ElMessage.error('请至少添加一个制作步骤')
      return
    }
    
    submitting.value = true
    
    const recipeData = {
      ...recipeForm,
      ingredients: validIngredients,
      steps: validSteps
    }
    
    const recipeId = await createRecipe(recipeData)
    ElMessage.success('菜谱创建成功')
    router.push(`/recipes/${recipeId}`)
  } catch (error) {
    console.error('创建菜谱失败:', error)
  } finally {
    submitting.value = false
  }
}

const handleReset = () => {
  recipeFormRef.value?.resetFields()
  Object.assign(recipeForm, {
    title: '',
    description: '',
    coverImageUrl: '',
    cookingTime: 30,
    difficulty: 1,
    servings: 2,
    ingredients: [{ ingredientName: '', quantity: '' }],
    steps: [{ stepNumber: 1, instruction: '', imageUrl: '' }]
  })
}
</script>

<style scoped>
.create-recipe-container {
  max-width: 800px;
  margin: 0 auto;
}

.cover-uploader .cover-image {
  width: 178px;
  height: 178px;
  display: block;
  border-radius: 6px;
}

.cover-uploader .cover-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.ingredients-section,
.steps-section {
  width: 100%;
}

.ingredient-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.step-item {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  align-items: flex-start;
}

.step-number {
  width: 32px;
  height: 32px;
  background: #409eff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-image {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border-radius: 6px;
}
</style>
