<template>
  <MainLayout>
    <div class="recipe-detail-container" v-if="recipe">
      <el-row :gutter="32">
        <el-col :span="12">
          <div class="recipe-image">
            <img :src="recipe.coverImageUrl || '/default-recipe.jpg'" :alt="recipe.title" />
          </div>
        </el-col>
        <el-col :span="12">
          <div class="recipe-header">
            <h1>{{ recipe.title }}</h1>
            <p class="description">{{ recipe.description }}</p>
            
            <div class="recipe-stats">
              <div class="stat-item">
                <el-icon><Timer /></el-icon>
                <span>{{ recipe.cookingTime }}分钟</span>
              </div>
              <div class="stat-item">
                <el-icon><User /></el-icon>
                <span>{{ recipe.servings }}人份</span>
              </div>
              <div class="stat-item">
                <el-icon><Star /></el-icon>
                <span>{{ recipe.estimatedCalories }}卡路里</span>
              </div>
              <div class="stat-item difficulty">
                {{ getDifficultyText(recipe.difficulty) }}
              </div>
            </div>

            <div class="action-buttons">
              <el-button
                type="primary"
                @click="toggleFavorite"
                :icon="recipe.isFavorited ? 'StarFilled' : 'Star'"
              >
                {{ recipe.isFavorited ? '已收藏' : '收藏' }}
              </el-button>
              <el-button @click="addToMealPlan">
                <el-icon><Calendar /></el-icon>
                添加到餐食计划
              </el-button>
            </div>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="32" class="content-section">
        <el-col :span="12">
          <el-card>
            <template #header>
              <h3>食材清单</h3>
            </template>
            <div class="ingredients-list">
              <div
                v-for="ingredient in recipe.ingredients"
                :key="ingredient.id"
                class="ingredient-item"
              >
                <span class="ingredient-name">{{ ingredient.ingredientName }}</span>
                <span class="ingredient-quantity">{{ ingredient.quantity }}</span>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card>
            <template #header>
              <h3>制作步骤</h3>
            </template>
            <div class="steps-list">
              <div
                v-for="step in recipe.steps"
                :key="step.id"
                class="step-item"
              >
                <div class="step-number">{{ step.stepNumber }}</div>
                <div class="step-content">
                  <p>{{ step.instruction }}</p>
                  <img v-if="step.imageUrl" :src="step.imageUrl" :alt="`步骤${step.stepNumber}`" />
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <div v-else-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>

    <div v-else class="error-container">
      <el-empty description="菜谱不存在或已被删除" />
    </div>

    <!-- 添加到餐食计划对话框 -->
    <el-dialog v-model="mealPlanDialogVisible" title="添加到餐食计划" width="400px">
      <el-form :model="mealPlanForm" label-width="80px">
        <el-form-item label="日期">
          <el-date-picker
            v-model="mealPlanForm.planDate"
            type="date"
            placeholder="选择日期"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="餐别">
          <el-select v-model="mealPlanForm.mealType" placeholder="选择餐别" style="width: 100%">
            <el-option label="早餐" :value="1" />
            <el-option label="午餐" :value="2" />
            <el-option label="晚餐" :value="3" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="mealPlanDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmAddToMealPlan" :loading="addingToMealPlan">
          确定
        </el-button>
      </template>
    </el-dialog>
  </MainLayout>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import MainLayout from '@/components/Layout/MainLayout.vue'
import { getRecipeDetail, favoriteRecipe, unfavoriteRecipe } from '@/api/recipe'
import { addMealPlan } from '@/api/plan'
import type { Recipe } from '@/types/recipe'
import type { MealPlanRequest } from '@/types/plan'

const route = useRoute()
const router = useRouter()

const loading = ref(true)
const recipe = ref<Recipe | null>(null)
const mealPlanDialogVisible = ref(false)
const addingToMealPlan = ref(false)

const mealPlanForm = reactive<MealPlanRequest>({
  recipeId: 0,
  planDate: '',
  mealType: 1
})

const loadRecipe = async () => {
  try {
    const id = Number(route.params.id)
    if (!id) {
      router.push('/recipes')
      return
    }
    
    loading.value = true
    const recipeData = await getRecipeDetail(id)
    recipe.value = recipeData
    mealPlanForm.recipeId = id
  } catch (error) {
    console.error('加载菜谱详情失败:', error)
    recipe.value = null
  } finally {
    loading.value = false
  }
}

const toggleFavorite = async () => {
  if (!recipe.value) return
  
  try {
    if (recipe.value.isFavorited) {
      await unfavoriteRecipe(recipe.value.id)
      recipe.value.isFavorited = false
      ElMessage.success('已取消收藏')
    } else {
      await favoriteRecipe(recipe.value.id)
      recipe.value.isFavorited = true
      ElMessage.success('收藏成功')
    }
  } catch (error) {
    console.error('操作失败:', error)
  }
}

const addToMealPlan = () => {
  mealPlanDialogVisible.value = true
}

const confirmAddToMealPlan = async () => {
  try {
    addingToMealPlan.value = true
    await addMealPlan(mealPlanForm)
    ElMessage.success('已添加到餐食计划')
    mealPlanDialogVisible.value = false
  } catch (error) {
    console.error('添加到餐食计划失败:', error)
  } finally {
    addingToMealPlan.value = false
  }
}

const getDifficultyText = (difficulty: number) => {
  const map = { 1: '简单', 2: '中等', 3: '困难' }
  return map[difficulty as keyof typeof map] || '未知'
}

onMounted(() => {
  loadRecipe()
})
</script>

<style scoped>
.recipe-detail-container {
  max-width: 1200px;
  margin: 0 auto;
}

.recipe-image {
  width: 100%;
  height: 400px;
  border-radius: 12px;
  overflow: hidden;
}

.recipe-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.recipe-header h1 {
  font-size: 32px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

.description {
  font-size: 16px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 24px;
}

.recipe-stats {
  display: flex;
  gap: 24px;
  margin-bottom: 32px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #666;
}

.stat-item.difficulty {
  background: #f0f0f0;
  padding: 4px 12px;
  border-radius: 16px;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  gap: 16px;
}

.content-section {
  margin-top: 32px;
}

.ingredients-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.ingredient-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.ingredient-name {
  font-weight: 500;
  color: #333;
}

.ingredient-quantity {
  color: #666;
  font-size: 14px;
}

.steps-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.step-item {
  display: flex;
  gap: 16px;
}

.step-number {
  width: 32px;
  height: 32px;
  background: #409eff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-content p {
  margin-bottom: 12px;
  line-height: 1.6;
  color: #333;
}

.step-content img {
  width: 100%;
  max-width: 200px;
  border-radius: 8px;
}

.loading-container,
.error-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 0;
}
</style>
