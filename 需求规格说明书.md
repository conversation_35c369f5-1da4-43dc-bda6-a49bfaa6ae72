### **健康饮食推荐平台 - 需求规格说明书 (PRD)**
**项目名称:** 基于微服务架构与AIGC的健康饮食推荐平台

------



### **1. 项目概述**

#### **1.1 项目愿景**

本项目旨在打造一个以“健康饮食”为核心的智能化菜谱平台。通过整合个性化健康管理、社区菜谱分享以及AIGC（人工智能生成内容）技术，我们致力于为用户提供从健康目标设定、智能菜谱生成、饮食计划制定到购物清单管理的一站式解决方案，让健康饮食变得简单、科学和充满乐趣。

#### **1.2 目标用户**

- 关注健康的普通大众，包括健身爱好者、减脂/增肌人群、家庭主厨等。
- 希望通过科学饮食改善生活质量的用户。

#### **1.3 核心价值**

- **个性化：** 基于用户健康数据提供定制化的营养建议。
- **智能化：** 利用AIGC技术，根据用户零散的需求快速生成创意菜谱。
- **便捷化：** 自动化周餐食计划和购物清单，简化生活流程。

------



### **2. 系统设计与架构**

#### **2.1 技术架构**

- **架构模式:** 微服务架构
- **后端:** Spring Cloud Alibaba (Nacos, Sentinel, Gateway), Spring Boot, MyBatis-Plus
- **数据库:** MySQL (业务数据), Redis (缓存、Session、热点数据)
- **消息队列:** RabbitMQ (用于服务间异步通信，如AIGC任务处理)
- **前端:** Vue3, Element Plus, Axios
- **部署:** 容器化部署 (如 Docker)

#### **2.2 微服务划分 (建议)**

为了实现高内聚、低耦合，建议将系统拆分为以下几个核心微服务：

1. **gateway-service (API网关):** 统一入口，负责请求路由、鉴权、限流。
2. **auth-service (认证中心):** 负责用户注册、登录、Token生成与校验。
3. **user-service (用户服务):** 管理用户基本信息和健康档案。
4. **recipe-service (菜谱服务):** 核心服务，负责菜谱的CRUD、搜索、分类、营养估算。
5. **plan-service (计划服务):** 负责周餐食计划的制定和智能购物清单的生成。
6. **aigc-service (AIGC服务):** 独立的AIGC代理服务，负责与外部大模型API交互，处理菜谱生成请求，并通过RabbitMQ异步返回结果。
7. **admin-service (后台管理服务):** 提供给管理员使用的后台管理API。

------



### **3. 用户角色与权限**









| 角色                | 权限描述                                                     |
| ------------------- | ------------------------------------------------------------ |
| **普通用户 (USER)** | - 注册/登录<br>- 管理个人健康档案 (FR-1)<br>- 发布、浏览、搜索菜谱 (FR-2)<br>- 使用AIGC生成菜谱 (FR-3)<br>- 查看菜谱营养信息 (FR-4)<br>- 制定/查看周餐食计划 (FR-5)<br>- 生成/查看购物清单 (FR-6)<br>- 收藏/取消收藏菜谱 (FR-7) |
| **管理员 (ADMIN)**  | - 登录后台管理系统<br>- 查看所有用户发布的菜谱<br>- 删除违规或不适宜的菜谱 (FR-8)<br>- 查看平台基础统计数据（如用户总数、菜谱总数） |

------



### **4. 功能性需求 (Functional Requirements)**

以下是每个核心功能的详细拆解，AI Agent应按照这些规格进行开发。

#### **FR-1: 个性化健康档案**

- **用户故事:** 作为一名用户，我希望能录入我的身体数据和健康目标，以便系统能给我一个科学的每日热量摄入建议。
- **实现细节:**
  1. **输入界面:** 提供一个表单，包含以下字段：
     - 性别 (单选：男/女)
     - 年龄 (数字输入)
     - 身高 (cm, 数字输入)
     - 体重 (kg, 数字输入)
     - 运动频率 (下拉选择：基本不动, 每周1-3次, 每周3-5次, 每周6-7次)
     - 健康目标 (下拉选择：减脂, 保持体重, 增肌)
  2. **计算逻辑:**
     - 根据 **Mifflin-St Jeor公式**（或Harris-Benedict公式）计算基础代谢率 (BMR)。
     - BMR = (10 * 体重kg) + (6.25 * 身高cm) - (5 * 年龄) + (性别调整值) (男+5, 女-161)
     - 根据运动频率计算每日总消耗热量 (TDEE): TDEE = BMR * 活动系数。
     - 根据健康目标调整每日推荐摄入卡路里：
       - 减脂: TDEE - 300 to TDEE - 500
       - 保持: TDEE
       - 增肌: TDEE + 300 to TDEE + 500
  3. **数据存储:** 将用户的健康数据和计算出的目标卡路里存入user_health_profile表。
  4. **展示:** 在用户的个人中心页面，醒目地展示计算出的“每日推荐摄入XXX千卡”。

#### **FR-2: 菜谱浏览与创建**

- **用户故事:** 作为一名用户，我可以分享我自己的拿手好菜，也可以浏览和搜索别人分享的菜谱。
- **实现细节:**
  1. **创建/编辑菜谱:**
     - 表单包含：菜名（必填）、封面图（图片上传）、简介、食材列表（可动态增删，每项包含食材名和用量，如“鸡胸肉 200g”）、制作步骤（可动态增删，每项包含步骤描述和可选的步骤图片）。
  2. **菜谱列表页:**
     - 以卡片形式瀑布流展示所有菜谱，每张卡片显示封面图、菜名、发布者。
     - 支持分页加载。
  3. **菜谱详情页:**
     - 展示菜谱的所有信息：封面图、菜名、简介、食材清单、详细步骤、发布者、发布时间、估算热量(FR-4)、收藏按钮(FR-7)。
  4. **搜索功能:**
     - 在列表页顶部提供一个搜索框。
     - 用户输入关键词后，后端根据菜名进行模糊查询 (LIKE %keyword%)。

#### **FR-3: AIGC智能菜谱生成**

- **用户故事:** 作为一名用户，当我不知道吃什么时，我希望能通过简单的描述（比如“冰箱里有什么”），让AI帮我创造一个新菜谱。
- **实现细节:**
  1. **输入界面:** 提供一个简洁的文本输入框和“智能生成”按钮。Placeholder提示用户输入，如：“牛肉，西兰花，减脂餐”。
  2. **后端处理流程 (异步):**
     - **aigc-service** 接收到前端请求。
     - **构建Prompt:** 将用户的输入包装成一个结构化的Prompt。例如："请根据以下食材和要求，为我生成一份健康菜谱。要求：[用户输入]。请以JSON格式返回，包含菜名(title)、所需食材(ingredients: [{name, quantity}])和制作步骤(steps: [string])。"
     - **调用外部API:** 通过HTTP Client调用外部AIGC模型API（如OpenAI GPT, Kimi等）。
     - **异步通知:** 请求发出后，立即返回一个任务ID给前端，告知“正在生成中”。同时，将生成任务放入 **RabbitMQ** 消息队列。
     - **处理结果:** aigc-service的消费者监听到消息，当AIGC模型返回结果后，解析返回的JSON数据，并将其存入recipes表（标记为AI生成）。可以通过WebSocket或轮询方式通知前端生成完毕。
  3. **前端展示:** 前端收到生成成功的通知后，跳转到该AI生成菜谱的详情页。

#### **FR-4: 菜谱营养成分估算**

- **用户故事:** 作为一名用户，我希望在看菜谱时能知道它的大概热量，以帮助我更好地控制饮食。
- **实现细节:**
  1. **数据基础:** 需要一个ingredient_nutrition表，预先存储常见食材的单位热量（如每100g的热量）。
  2. **计算逻辑:**
     - 当一个新菜谱被创建或编辑时，**recipe-service** 触发计算。
     - 遍历菜谱的食材列表。
     - 对每个食材，从ingredient_nutrition表中查找其单位热量。
     - 总热量 = Σ (食材用量(g) / 100 * 单位热量)。
     - **注意:** 需要处理单位换算（如“个”、“勺”、“块”等估算为克）。初期可以只支持“克(g)”为单位，简化实现。
  3. **存储与展示:** 将估算出的总热量存入recipes表的estimated_calories字段，并在菜谱详情页显示。

#### **FR-5: 周餐食计划**

- **用户故事:** 作为一名用户，我希望能将喜欢的菜谱安排在一周的计划中，方便我直观地看到我下周要吃什么。
- **实现细节:**
  1. **界面:**
     - 提供一个以“周”为单位的日历视图（周一到周日）。
     - 每天分为“早餐”、“午餐”、“晚餐”三个槽位。
  2. **添加操作:**
     - 在菜谱详情页，提供“添加到餐食计划”按钮。
     - 点击后弹出选择器，让用户选择要添加到的“日期”和“餐别”（早/中/晚）。
  3. **数据存储:** 在meal_plans表中记录用户、日期、餐别和对应的recipe_id。
  4. **展示:** 在周历视图中，已安排的槽位会显示对应菜谱的名称和缩略图，点击可跳转到菜谱详情。

#### **FR-6: 智能购物清单**

- **用户故事:** 作为一名用户，在我制定好一周的饮食计划后，我希望能一键生成所有需要购买的食材清单，避免遗漏。
- **实现细节:**
  1. **触发:** 在餐食计划页面，提供一个“生成购物清单”的按钮，并允许用户选择时间范围（如“本周”或自定义日期）。
  2. **后端逻辑 (plan-service):**
     - 根据用户选择的时间范围，获取该范围内的所有餐食计划中的recipe_id。
     - 遍历这些recipe_id，查询recipe-service获取每个菜谱的食材列表。
     - **汇总与去重:**
       - 创建一个Map (IngredientName -> {total_quantity, unit})。
       - 遍历所有食材，如果Map中已存在该食材且单位相同，则累加数量；否则，新增。
       - 对于单位不同的同名食材（如“水 200ml”和“水 1升”），初期可分别列出，后期可做单位换算。
  3. **展示:**
     - 生成一个清单页面，清晰地列出所有汇总后的食材及其总量。
     - 提供复选框，方便用户在购物时勾选已购买的项。

#### **FR-7: 菜谱收藏**

- **用户故事:** 作为一名用户，我可以收藏我喜欢的菜谱，方便以后快速找到。
- **实现细节:**
  1. **操作:** 在菜谱详情页和列表页的卡片上，提供一个“收藏”图标（如心形）。
  2. **状态:** 图标状态应为“未收藏”和“已收藏”两种，点击切换。
  3. **后端:** 创建一个favorites关联表 (user_id, recipe_id)。点击收藏则插入记录，取消收藏则删除记录。
  4. **我的收藏:** 在个人中心提供“我的收藏”入口，展示该用户收藏的所有菜谱列表。

#### **FR-8: 后台内容管理**

- **用户故事:** 作为一名管理员，我需要一个后台系统来管理平台上的内容，确保其合规性。
- **实现细节:**
  1. **独立后台:** 开发一个独立的后台管理前端（可以使用现成的Vue admin模板）。
  2. **登录:** 管理员使用专属账号密码登录。
  3. **菜谱管理:**
     - 提供一个表格，展示所有用户发布的菜谱列表，包含ID、菜名、发布者、发布时间等。
     - 支持按菜名、发布者进行搜索和筛选。
     - 每一行提供“查看详情”和“删除”操作。
     - “删除”操作前应有二次确认弹窗，防止误操作。

------



### **5. 非功能性需求 (Non-Functional Requirements)**

- **性能:** API平均响应时间应低于500ms，页面主要内容加载时间低于2秒。
- **安全性:**
  - 用户密码必须加密存储（如BCrypt）。
  - 所有API都应通过API网关进行访问，并进行JWT鉴权。
  - 对用户输入进行严格校验，防止XSS、SQL注入等攻击。
  - 使用Sentinel进行服务熔断和限流，防止服务雪崩。
- **可用性:** 系统核心服务可用性需达到99.9%。
- **可维护性:** 代码注释清晰，遵循统一的编码规范，微服务职责明确。
- **用户体验:** 前端界面应为响应式设计，在主流PC和移动端浏览器上均有良好体验。

------



### **6. 数据模型 (核心表结构)**

Generated sql

```
-- 用户表
CREATE TABLE `users` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
  `username` VARCHAR(50) UNIQUE NOT NULL,
  `password` VARCHAR(255) NOT NULL,
  `role` VARCHAR(20) DEFAULT 'USER', -- 'USER', 'ADMIN'
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户健康档案表
CREATE TABLE `user_health_profiles` (
  `user_id` BIGINT PRIMARY KEY,
  `gender` TINYINT, -- 0:女, 1:男
  `age` INT,
  `height` DECIMAL(5,2),
  `weight` DECIMAL(5,2),
  `activity_level` INT, -- 对应运动频率
  `goal` INT, -- 对应健康目标
  `target_calories` INT, -- 计算出的每日推荐卡路里
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`)
);

-- 菜谱表
CREATE TABLE `recipes` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
  `user_id` BIGINT NOT NULL,
  `title` VARCHAR(255) NOT NULL,
  `description` TEXT,
  `cover_image_url` VARCHAR(255),
  `estimated_calories` INT,
  `is_ai_generated` BOOLEAN DEFAULT FALSE,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`)
);

-- 食材表 (主数据)
CREATE TABLE `ingredients` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
  `name` VARCHAR(100) UNIQUE NOT NULL,
  `calories_per_100g` INT -- 每100g的卡路里
);

-- 菜谱-食材关联表
CREATE TABLE `recipe_ingredients` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
  `recipe_id` BIGINT NOT NULL,
  `ingredient_name` VARCHAR(100) NOT NULL, -- 直接存名字，方便展示
  `quantity` VARCHAR(50) NOT NULL, -- 如 "200g", "1个", "适量"
  FOREIGN KEY (`recipe_id`) REFERENCES `recipes`(`id`)
);

-- 菜谱步骤表
CREATE TABLE `recipe_steps` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
  `recipe_id` BIGINT NOT NULL,
  `step_number` INT NOT NULL,
  `description` TEXT NOT NULL,
  `image_url` VARCHAR(255),
  FOREIGN KEY (`recipe_id`) REFERENCES `recipes`(`id`)
);

-- 收藏表
CREATE TABLE `favorites` (
  `user_id` BIGINT NOT NULL,
  `recipe_id` BIGINT NOT NULL,
  PRIMARY KEY (`user_id`, `recipe_id`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`),
  FOREIGN KEY (`recipe_id`) REFERENCES `recipes`(`id`)
);

-- 餐食计划表
CREATE TABLE `meal_plan_entries` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
  `user_id` BIGINT NOT NULL,
  `recipe_id` BIGINT NOT NULL,
  `plan_date` DATE NOT NULL,
  `meal_type` INT NOT NULL, -- 1:早餐, 2:午餐, 3:晚餐
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`),
  FOREIGN KEY (`recipe_id`) REFERENCES `recipes`(`id`)
);
```

Use code [with caution](https://support.google.com/legal/answer/13505487).SQL

------



### **7. AI Agent执行任务建议**

1. **环境搭建:** 初始化Spring Cloud Alibaba项目，配置Nacos、Sentinel、Gateway。
2. **服务开发 (由简到繁):**
   - **第一步:** 开发 auth-service 和 user-service，实现用户注册、登录和健康档案管理(FR-1)。
   - **第二步:** 开发 recipe-service，实现菜谱的CRUD、搜索(FR-2)和收藏功能(FR-7)。
   - **第三步:** 完善 recipe-service，加入营养估算(FR-4)逻辑。
   - **第四步:** 开发 aigc-service，配置RabbitMQ，集成外部AI模型API，实现智能菜谱生成(FR-3)。
   - **第五步:** 开发 plan-service，实现周餐食计划(FR-5)和购物清单(FR-6)。
   - **第六步:** 开发 admin-service 和后台管理前端，实现内容管理(FR-8)。
3. **前端开发:** 与后端并行开发，根据API接口文档进行页面组件的开发和联调。
4. **测试与部署:** 编写单元测试和集成测试，最后进行容器化部署。