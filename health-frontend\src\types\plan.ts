export interface MealPlan {
  id: number
  recipeId: number
  recipeTitle: string
  recipeCoverImage?: string
  recipeCalories?: number
  planDate: string
  mealType: number // 1-早餐，2-午餐，3-晚餐
  mealTypeName: string
}

export interface DailyMealPlan {
  date: string
  breakfast?: MealPlan
  lunch?: MealPlan
  dinner?: MealPlan
  totalCalories: number
}

export interface WeeklyMealPlan {
  weekStartDate: string
  weekEndDate: string
  dailyPlans: Record<string, DailyMealPlan>
}

export interface MealPlanRequest {
  recipeId: number
  planDate: string
  mealType: number
}

export interface ShoppingListRequest {
  startDate: string
  endDate: string
}

export interface ShoppingListItem {
  name: string
  totalQuantity: string
  unit: string
  purchased: boolean
}

export interface ShoppingList {
  startDate: string
  endDate: string
  ingredients: ShoppingListItem[]
}
