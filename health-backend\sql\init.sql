-- 创建数据库
CREATE DATABASE IF NOT EXISTS healthy_diet_platform DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE healthy_diet_platform;

-- 用户表
CREATE TABLE `users` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
  `username` VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
  `password` VARCHAR(255) NOT NULL COMMENT '密码',
  `role` VARCHAR(20) DEFAULT 'USER' COMMENT '角色：USER-普通用户, ADMIN-管理员',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 用户健康档案表
CREATE TABLE `user_health_profiles` (
  `user_id` BIGINT PRIMARY KEY COMMENT '用户ID',
  `gender` TINYINT COMMENT '性别：0-女, 1-男',
  `age` INT COMMENT '年龄',
  `height` DECIMAL(5,2) COMMENT '身高(cm)',
  `weight` DECIMAL(5,2) COMMENT '体重(kg)',
  `activity_level` INT COMMENT '运动频率：1-基本不动, 2-每周1-3次, 3-每周3-5次, 4-每周6-7次',
  `goal` INT COMMENT '健康目标：1-减脂, 2-保持体重, 3-增肌',
  `target_calories` INT COMMENT '每日推荐卡路里',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户健康档案表';

-- 菜谱表
CREATE TABLE `recipes` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
  `user_id` BIGINT NOT NULL COMMENT '发布者用户ID',
  `title` VARCHAR(255) NOT NULL COMMENT '菜谱名称',
  `description` TEXT COMMENT '菜谱简介',
  `cover_image_url` VARCHAR(255) COMMENT '封面图片URL',
  `estimated_calories` INT COMMENT '估算热量',
  `is_ai_generated` BOOLEAN DEFAULT FALSE COMMENT '是否AI生成',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_title` (`title`),
  INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='菜谱表';

-- 食材表 (主数据)
CREATE TABLE `ingredients` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
  `name` VARCHAR(100) UNIQUE NOT NULL COMMENT '食材名称',
  `calories_per_100g` INT COMMENT '每100g的卡路里',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='食材表';

-- 菜谱-食材关联表
CREATE TABLE `recipe_ingredients` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
  `recipe_id` BIGINT NOT NULL COMMENT '菜谱ID',
  `ingredient_name` VARCHAR(100) NOT NULL COMMENT '食材名称',
  `quantity` VARCHAR(50) NOT NULL COMMENT '用量，如：200g, 1个, 适量',
  FOREIGN KEY (`recipe_id`) REFERENCES `recipes`(`id`) ON DELETE CASCADE,
  INDEX `idx_recipe_id` (`recipe_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='菜谱食材关联表';

-- 菜谱步骤表
CREATE TABLE `recipe_steps` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
  `recipe_id` BIGINT NOT NULL COMMENT '菜谱ID',
  `step_number` INT NOT NULL COMMENT '步骤序号',
  `description` TEXT NOT NULL COMMENT '步骤描述',
  `image_url` VARCHAR(255) COMMENT '步骤图片URL',
  FOREIGN KEY (`recipe_id`) REFERENCES `recipes`(`id`) ON DELETE CASCADE,
  INDEX `idx_recipe_id` (`recipe_id`),
  INDEX `idx_step_number` (`step_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='菜谱步骤表';

-- 收藏表
CREATE TABLE `favorites` (
  `user_id` BIGINT NOT NULL COMMENT '用户ID',
  `recipe_id` BIGINT NOT NULL COMMENT '菜谱ID',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间',
  PRIMARY KEY (`user_id`, `recipe_id`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`recipe_id`) REFERENCES `recipes`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='收藏表';

-- 餐食计划表
CREATE TABLE `meal_plan_entries` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
  `user_id` BIGINT NOT NULL COMMENT '用户ID',
  `recipe_id` BIGINT NOT NULL COMMENT '菜谱ID',
  `plan_date` DATE NOT NULL COMMENT '计划日期',
  `meal_type` INT NOT NULL COMMENT '餐别：1-早餐, 2-午餐, 3-晚餐',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`recipe_id`) REFERENCES `recipes`(`id`) ON DELETE CASCADE,
  INDEX `idx_user_date` (`user_id`, `plan_date`),
  INDEX `idx_meal_type` (`meal_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='餐食计划表';

-- 插入初始管理员用户 (密码: admin123)
INSERT INTO `users` (`username`, `password`, `role`) VALUES
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', 'ADMIN');

-- 插入常见食材营养数据
INSERT INTO `ingredients` (`name`, `calories_per_100g`) VALUES
('鸡胸肉', 165),
('牛肉', 250),
('猪肉', 242),
('鸡蛋', 155),
('大米', 130),
('面条', 137),
('土豆', 76),
('西兰花', 34),
('胡萝卜', 41),
('白菜', 17),
('番茄', 18),
('黄瓜', 15),
('苹果', 52),
('香蕉', 89),
('牛奶', 54),
('酸奶', 59),
('豆腐', 76),
('花生油', 899),
('橄榄油', 884),
('盐', 0),
('生抽', 63),
('老抽', 65),
('料酒', 103),
('白糖', 387),
('蜂蜜', 304);
