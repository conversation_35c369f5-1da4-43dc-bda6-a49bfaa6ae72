@echo off
echo ========================================
echo 健康饮食推荐平台 - 一键启动脚本
echo ========================================

echo.
echo [1/4] 启动基础设施服务 (MySQL, Redis, RabbitMQ, Nacos)...
docker-compose up -d

echo.
echo [2/4] 等待基础设施服务启动完成...
timeout /t 30

echo.
echo [3/4] 编译后端项目...
cd health-backend
mvn clean compile
if %errorlevel% neq 0 (
    echo 错误: 项目编译失败
    pause
    exit /b 1
)

echo.
echo [4/4] 启动微服务...
echo 正在启动认证服务 (8081)...
start "认证服务" powershell -Command "cd 'auth-service'; mvn spring-boot:run"

timeout /t 10

echo 正在启动用户服务 (8082)...
start "用户服务" powershell -Command "cd 'user-service'; mvn spring-boot:run"

timeout /t 10

echo 正在启动网关服务 (8080)...
start "网关服务" powershell -Command "cd 'gateway-service'; mvn spring-boot:run"

echo.
echo ========================================
echo 服务启动完成！
echo ========================================
echo.
echo 服务访问地址:
echo - 网关服务: http://localhost:8080
echo - Nacos控制台: http://localhost:8848/nacos (用户名/密码: nacos/nacos)
echo - RabbitMQ管理界面: http://localhost:15672 (用户名/密码: guest/guest)
echo.
echo 前端服务启动:
echo cd health-frontend
echo pnpm run dev
echo.

pause
