package com.healthdiet.plan.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.healthdiet.plan.dto.*;
import com.healthdiet.plan.entity.MealPlanEntry;
import com.healthdiet.plan.feign.RecipeServiceClient;
import com.healthdiet.plan.mapper.MealPlanEntryMapper;
import com.healthdiet.plan.service.MealPlanService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 餐食计划服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MealPlanServiceImpl implements MealPlanService {
    
    private final MealPlanEntryMapper mealPlanEntryMapper;
    private final RecipeServiceClient recipeServiceClient;
    
    @Override
    public MealPlanResponse addMealPlan(Long userId, MealPlanRequest request) {
        // 检查该时间段是否已有餐食计划
        MealPlanEntry existing = mealPlanEntryMapper.selectByUserIdAndDateAndMealType(
                userId, request.getPlanDate(), request.getMealType());
        
        if (existing != null) {
            // 如果已存在，则更新
            existing.setRecipeId(request.getRecipeId());
            mealPlanEntryMapper.updateById(existing);
            return convertToResponse(existing);
        } else {
            // 如果不存在，则新增
            MealPlanEntry entry = new MealPlanEntry();
            entry.setUserId(userId);
            entry.setRecipeId(request.getRecipeId());
            entry.setPlanDate(request.getPlanDate());
            entry.setMealType(request.getMealType());
            
            mealPlanEntryMapper.insert(entry);
            return convertToResponse(entry);
        }
    }
    
    @Override
    public void removeMealPlan(Long userId, Long planId) {
        LambdaQueryWrapper<MealPlanEntry> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MealPlanEntry::getId, planId)
               .eq(MealPlanEntry::getUserId, userId);
        
        mealPlanEntryMapper.delete(wrapper);
    }
    
    @Override
    public WeeklyMealPlanResponse getWeeklyMealPlan(Long userId, LocalDate weekStartDate) {
        LocalDate weekEndDate = weekStartDate.plusDays(6);
        
        // 查询该周的所有餐食计划
        List<MealPlanEntry> entries = mealPlanEntryMapper.selectByUserIdAndDateRange(
                userId, weekStartDate, weekEndDate);
        
        // 按日期分组
        Map<LocalDate, List<MealPlanEntry>> entriesByDate = entries.stream()
                .collect(Collectors.groupingBy(MealPlanEntry::getPlanDate));
        
        // 构建响应
        WeeklyMealPlanResponse response = new WeeklyMealPlanResponse();
        response.setWeekStartDate(weekStartDate);
        response.setWeekEndDate(weekEndDate);
        
        Map<String, WeeklyMealPlanResponse.DailyMealPlan> dailyPlans = new HashMap<>();
        
        // 遍历一周的每一天
        for (int i = 0; i < 7; i++) {
            LocalDate date = weekStartDate.plusDays(i);
            String dateKey = date.toString();
            
            WeeklyMealPlanResponse.DailyMealPlan dailyPlan = new WeeklyMealPlanResponse.DailyMealPlan();
            dailyPlan.setDate(date);
            
            List<MealPlanEntry> dayEntries = entriesByDate.getOrDefault(date, new ArrayList<>());
            int totalCalories = 0;
            
            for (MealPlanEntry entry : dayEntries) {
                MealPlanResponse mealResponse = convertToResponse(entry);
                totalCalories += (mealResponse.getRecipeCalories() != null ? mealResponse.getRecipeCalories() : 0);
                
                switch (entry.getMealType()) {
                    case 1 -> dailyPlan.setBreakfast(mealResponse);
                    case 2 -> dailyPlan.setLunch(mealResponse);
                    case 3 -> dailyPlan.setDinner(mealResponse);
                }
            }
            
            dailyPlan.setTotalCalories(totalCalories);
            dailyPlans.put(dateKey, dailyPlan);
        }
        
        response.setDailyPlans(dailyPlans);
        return response;
    }
    
    @Override
    public ShoppingListResponse generateShoppingList(Long userId, ShoppingListRequest request) {
        // 查询指定日期范围内的所有餐食计划
        List<MealPlanEntry> entries = mealPlanEntryMapper.selectByUserIdAndDateRange(
                userId, request.getStartDate(), request.getEndDate());
        
        // 获取所有菜谱ID
        Set<Long> recipeIds = entries.stream()
                .map(MealPlanEntry::getRecipeId)
                .collect(Collectors.toSet());
        
        // 汇总食材
        Map<String, ShoppingListResponse.IngredientItem> ingredientMap = new HashMap<>();
        
        for (Long recipeId : recipeIds) {
            try {
                List<RecipeServiceClient.RecipeIngredient> ingredients = 
                        recipeServiceClient.getRecipeIngredients(recipeId);
                
                for (RecipeServiceClient.RecipeIngredient ingredient : ingredients) {
                    String name = ingredient.getIngredientName();
                    String quantity = ingredient.getQuantity();
                    
                    if (ingredientMap.containsKey(name)) {
                        // 如果已存在该食材，累加数量（简化处理，实际应该解析单位）
                        ShoppingListResponse.IngredientItem existing = ingredientMap.get(name);
                        existing.setTotalQuantity(existing.getTotalQuantity() + ", " + quantity);
                    } else {
                        // 新增食材
                        ShoppingListResponse.IngredientItem item = new ShoppingListResponse.IngredientItem();
                        item.setName(name);
                        item.setTotalQuantity(quantity);
                        item.setUnit(""); // 简化处理
                        ingredientMap.put(name, item);
                    }
                }
            } catch (Exception e) {
                log.warn("获取菜谱{}的食材信息失败: {}", recipeId, e.getMessage());
            }
        }
        
        ShoppingListResponse response = new ShoppingListResponse();
        response.setStartDate(request.getStartDate());
        response.setEndDate(request.getEndDate());
        response.setIngredients(new ArrayList<>(ingredientMap.values()));
        
        return response;
    }
    
    /**
     * 转换为响应DTO
     */
    private MealPlanResponse convertToResponse(MealPlanEntry entry) {
        MealPlanResponse response = new MealPlanResponse();
        response.setId(entry.getId());
        response.setRecipeId(entry.getRecipeId());
        response.setPlanDate(entry.getPlanDate());
        response.setMealType(entry.getMealType());
        response.setMealTypeName(getMealTypeName(entry.getMealType()));
        
        // 获取菜谱信息
        try {
            RecipeServiceClient.RecipeInfo recipeInfo = recipeServiceClient.getRecipeById(entry.getRecipeId());
            if (recipeInfo != null) {
                response.setRecipeTitle(recipeInfo.getTitle());
                response.setRecipeCoverImage(recipeInfo.getCoverImageUrl());
                response.setRecipeCalories(recipeInfo.getEstimatedCalories());
            }
        } catch (Exception e) {
            log.warn("获取菜谱{}信息失败: {}", entry.getRecipeId(), e.getMessage());
            response.setRecipeTitle("菜谱信息获取失败");
        }
        
        return response;
    }
    
    /**
     * 获取餐别名称
     */
    private String getMealTypeName(Integer mealType) {
        return switch (mealType) {
            case 1 -> "早餐";
            case 2 -> "午餐";
            case 3 -> "晚餐";
            default -> "未知";
        };
    }
}
