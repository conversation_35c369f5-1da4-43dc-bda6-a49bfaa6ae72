package com.healthdiet.auth.controller;

import com.healthdiet.auth.dto.LoginRequest;
import com.healthdiet.auth.dto.LoginResponse;
import com.healthdiet.auth.dto.RegisterRequest;
import com.healthdiet.auth.service.AuthService;
import com.healthdiet.common.result.Result;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 认证控制器
 */
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
public class AuthController {
    
    private final AuthService authService;
    
    /**
     * 用户注册
     */
    @PostMapping("/register")
    public Result<Void> register(@Valid @RequestBody RegisterRequest request) {
        try {
            authService.register(request);
            return Result.success("注册成功", null);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 用户登录
     */
    @PostMapping("/login")
    public Result<LoginResponse> login(@Valid @RequestBody LoginRequest request) {
        try {
            LoginResponse response = authService.login(request);
            return Result.success("登录成功", response);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 验证Token
     */
    @GetMapping("/validate")
    public Result<Boolean> validateToken(@RequestParam String token) {
        boolean isValid = authService.validateToken(token);
        return Result.success(isValid);
    }
    
    /**
     * 从Token获取用户ID
     */
    @GetMapping("/user-id")
    public Result<Long> getUserIdFromToken(@RequestParam String token) {
        Long userId = authService.getUserIdFromToken(token);
        return Result.success(userId);
    }
}
