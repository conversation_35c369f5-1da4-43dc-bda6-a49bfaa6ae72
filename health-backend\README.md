# 健康饮食推荐平台 - 后端服务

基于微服务架构与AIGC的健康饮食推荐平台后端服务

## 项目架构

### 技术栈
- **Spring Boot**: 3.2.5
- **Spring Cloud**: 2023.0.2  
- **Spring Cloud Alibaba**: 2023.0.1.0
- **MyBatis Plus**: 3.5.7
- **MySQL**: 8.0.33
- **Redis**: 3.3.1
- **Druid**: 1.2.22
- **JWT**: 0.11.5

### 微服务模块

1. **gateway-service (8080)** - API网关
   - 统一入口，请求路由
   - 跨域配置
   - 负载均衡

2. **auth-service (8081)** - 认证服务 ✅
   - 用户注册、登录
   - JWT Token生成与验证
   - Spring Security集成

3. **user-service (8082)** - 用户服务 ✅
   - 用户健康档案管理
   - BMR计算和目标卡路里推荐
   - BMI计算和健康评估

4. **recipe-service (8083)** - 菜谱服务 ✅
   - 菜谱CRUD操作
   - 菜谱搜索和分类
   - 收藏功能
   - 营养成分估算

5. **aigc-service (8085)** - AIGC服务 ✅
   - AI菜谱生成（集成DeepSeek API）
   - 异步任务处理（RabbitMQ）
   - 任务状态管理（Redis）

6. **plan-service (8084)** - 计划服务 📋
   - 周餐食计划管理
   - 智能购物清单生成

7. **admin-service (8086)** - 管理服务 📋
   - 后台内容管理
   - 用户管理
   - 数据统计

## 数据库设计

### 核心表结构
- `users` - 用户表
- `user_health_profiles` - 用户健康档案表
- `recipes` - 菜谱表
- `recipe_ingredients` - 菜谱食材关联表
- `recipe_steps` - 菜谱步骤表
- `ingredients` - 食材营养数据表
- `favorites` - 收藏表
- `meal_plan_entries` - 餐食计划表

## 部署说明

### 环境要求
- JDK 17+
- MySQL 8.0+ (用户名: root, 密码: mysql123)
- Redis 6.0+
- RabbitMQ 3.8+ (用于AIGC异步任务)
- Nacos 2.0+ (可选，测试时可禁用)

### 启动顺序
1. 启动 MySQL 和 Redis
2. 启动 RabbitMQ (AIGC服务需要)
3. 启动 Nacos (可选)
4. 执行数据库初始化脚本 `sql/init.sql`
5. 启动各个微服务
   - gateway-service
   - auth-service
   - user-service
   - recipe-service
   - aigc-service
   - 其他服务...

### API文档
- 网关地址: http://localhost:8080
- 认证服务: http://localhost:8080/api/auth
- 用户服务: http://localhost:8080/api/users
- 菜谱服务: http://localhost:8080/api/recipes

## 开发进度

- [x] 项目环境搭建与基础架构
- [x] 认证服务开发 (auth-service)
- [x] 用户服务开发 (user-service)
- [x] 菜谱服务核心功能 (recipe-service)
- [x] 菜谱营养估算功能
- [x] AIGC智能菜谱生成服务 (集成DeepSeek API)
- [ ] 餐食计划服务 (plan-service)
- [ ] 后台管理服务
- [ ] 前端应用开发
- [ ] 系统测试与部署

## 下一步计划

1. 开发餐食计划服务 (plan-service)
2. 实现周餐食计划和智能购物清单功能
3. 开发后台管理服务 (admin-service)
4. 开发前端应用 (Vue3 + Element Plus)
5. 系统集成测试和部署优化

## AIGC功能说明

- **AI模型**: 集成DeepSeek API (***********************************)
- **异步处理**: 使用RabbitMQ处理AI生成任务
- **状态管理**: 使用Redis缓存任务状态
- **备用方案**: API调用失败时使用本地模拟数据

## 快速测试

### 自动化脚本
```bash
# Windows环境
start-services.bat    # 编译项目并显示启动说明
health-check.bat      # 检查服务健康状态
test-api.bat         # 执行API功能测试
```

### 手动测试步骤
1. 启动MySQL和Redis服务
2. 执行数据库初始化脚本: `mysql -u root -p < sql/init.sql`
3. 启动各个微服务（可选择性启动Nacos）
4. 运行API测试脚本验证功能

### 测试用例
- ✅ 用户注册和登录
- ✅ JWT Token生成和验证
- ✅ 健康档案CRUD操作
- ✅ BMR和目标卡路里计算
- ✅ BMI计算和健康评估

详细测试说明请查看 `test-api.md`

## 注意事项

- 所有服务都需要先启动Nacos注册中心（测试时可禁用）
- 数据库连接信息需要根据实际环境调整
- JWT密钥在生产环境中应该使用更安全的配置方式
- Redis配置需要根据实际部署环境调整
