version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: healthy-diet-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: mysql123
      MYSQL_DATABASE: healthy_diet_platform
      MYSQL_USER: healthdiet
      MYSQL_PASSWORD: healthdiet123
    ports:
      - "3307:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./health-backend/sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - healthy-diet-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: healthy-diet-redis
    restart: always
    ports:
      - "6380:6379"
    volumes:
      - redis_data:/data
    networks:
      - healthy-diet-network

  # RabbitMQ消息队列
  rabbitmq:
    image: rabbitmq:3.12-management-alpine
    container_name: healthy-diet-rabbitmq
    restart: always
    environment:
      RABBITMQ_DEFAULT_USER: guest
      RABBITMQ_DEFAULT_PASS: guest
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - healthy-diet-network

  # Nacos注册中心和配置中心 - 已改为使用本地Nacos
  # 本地Nacos运行在 localhost:8848
  # 启动命令: startup.cmd -m standalone (Windows) 或 sh startup.sh -m standalone (Linux/Mac)

volumes:
  mysql_data:
  redis_data:
  rabbitmq_data:

networks:
  healthy-diet-network:
    driver: bridge
