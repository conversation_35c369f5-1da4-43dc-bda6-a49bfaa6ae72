package com.healthdiet.aigc.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.healthdiet.aigc.config.DeepSeekConfig;
import com.healthdiet.aigc.dto.*;
import com.healthdiet.aigc.service.AiModelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;

/**
 * AI模型服务实现类 - 集成DeepSeek API
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AiModelServiceImpl implements AiModelService {

    private final DeepSeekConfig deepSeekConfig;
    private final ObjectMapper objectMapper;
    private final WebClient webClient;
    
    @Override
    public AiRecipeData generateRecipe(RecipeGenerationRequest request) {
        log.info("开始生成菜谱，用户输入: {}", request.getPrompt());

        try {
            // 构建提示词
            String prompt = buildPrompt(request);

            // 调用DeepSeek API
            String aiResponse = callDeepSeekApi(prompt);

            // 解析AI响应为菜谱数据
            AiRecipeData recipeData = parseAiResponse(aiResponse);

            log.info("菜谱生成完成: {}", recipeData.getTitle());
            return recipeData;

        } catch (Exception e) {
            log.error("调用DeepSeek API失败，使用备用方案", e);
            // 如果API调用失败，使用备用的模拟数据
            return generateMockRecipe(request.getPrompt());
        }
    }

    /**
     * 构建提示词
     */
    private String buildPrompt(RecipeGenerationRequest request) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("请根据以下要求生成一个健康菜谱，要求以JSON格式返回：\n\n");
        prompt.append("用户需求：").append(request.getPrompt()).append("\n\n");
        prompt.append("请返回以下格式的JSON数据：\n");
        prompt.append("{\n");
        prompt.append("  \"title\": \"菜谱名称\",\n");
        prompt.append("  \"description\": \"菜谱简介\",\n");
        prompt.append("  \"ingredients\": [\n");
        prompt.append("    {\"name\": \"食材名称\", \"quantity\": \"用量\"}\n");
        prompt.append("  ],\n");
        prompt.append("  \"steps\": [\n");
        prompt.append("    {\"description\": \"制作步骤描述\"}\n");
        prompt.append("  ]\n");
        prompt.append("}\n\n");
        prompt.append("注意事项：\n");
        prompt.append("1. 食材用量要具体，如200g、1个、适量等\n");
        prompt.append("2. 制作步骤要详细清晰\n");
        prompt.append("3. 考虑营养搭配和健康因素\n");
        prompt.append("4. 只返回JSON数据，不要其他文字说明");

        return prompt.toString();
    }

    /**
     * 调用DeepSeek API
     */
    private String callDeepSeekApi(String prompt) {
        // 构建请求
        DeepSeekRequest request = new DeepSeekRequest();
        request.setModel(deepSeekConfig.getModel());
        request.setMaxTokens(deepSeekConfig.getMaxTokens());
        request.setTemperature(0.7);

        List<DeepSeekRequest.Message> messages = new ArrayList<>();
        messages.add(new DeepSeekRequest.Message("user", prompt));
        request.setMessages(messages);

        // 发送请求
        DeepSeekResponse response = webClient.post()
                .uri(deepSeekConfig.getBaseUrl() + "/chat/completions")
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + deepSeekConfig.getKey())
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .bodyValue(request)
                .retrieve()
                .bodyToMono(DeepSeekResponse.class)
                .timeout(Duration.ofMillis(deepSeekConfig.getTimeout()))
                .block();

        if (response != null && response.getChoices() != null && !response.getChoices().isEmpty()) {
            return response.getChoices().get(0).getMessage().getContent();
        }

        throw new RuntimeException("DeepSeek API返回空响应");
    }

    /**
     * 解析AI响应为菜谱数据
     */
    private AiRecipeData parseAiResponse(String aiResponse) {
        try {
            // 提取JSON部分（去除可能的前后文字）
            String jsonContent = extractJsonFromResponse(aiResponse);

            // 解析JSON
            return objectMapper.readValue(jsonContent, AiRecipeData.class);

        } catch (JsonProcessingException e) {
            log.error("解析AI响应失败: {}", aiResponse, e);
            throw new RuntimeException("解析AI响应失败", e);
        }
    }

    /**
     * 从响应中提取JSON内容
     */
    private String extractJsonFromResponse(String response) {
        // 查找JSON开始和结束位置
        int startIndex = response.indexOf("{");
        int endIndex = response.lastIndexOf("}");

        if (startIndex != -1 && endIndex != -1 && endIndex > startIndex) {
            return response.substring(startIndex, endIndex + 1);
        }

        // 如果没有找到完整的JSON，返回原始响应
        return response;
    }

    /**
     * 模拟生成菜谱数据（备用方案）
     */
    private AiRecipeData generateMockRecipe(String prompt) {
        AiRecipeData recipe = new AiRecipeData();
        
        // 根据关键词生成不同的菜谱
        if (prompt.contains("鸡胸肉") || prompt.contains("减脂")) {
            recipe.setTitle("香煎鸡胸肉配蔬菜");
            recipe.setDescription("低脂高蛋白的健康减脂餐，营养均衡，口感丰富");
            
            List<AiRecipeData.IngredientItem> ingredients = new ArrayList<>();
            ingredients.add(createIngredient("鸡胸肉", "200g"));
            ingredients.add(createIngredient("西兰花", "150g"));
            ingredients.add(createIngredient("胡萝卜", "100g"));
            ingredients.add(createIngredient("橄榄油", "10ml"));
            ingredients.add(createIngredient("盐", "适量"));
            ingredients.add(createIngredient("黑胡椒", "适量"));
            recipe.setIngredients(ingredients);
            
            List<AiRecipeData.StepItem> steps = new ArrayList<>();
            steps.add(createStep("鸡胸肉洗净，用厨房纸擦干，两面撒盐和黑胡椒腌制15分钟"));
            steps.add(createStep("西兰花切小朵，胡萝卜切片，分别焯水备用"));
            steps.add(createStep("平底锅刷少量橄榄油，中火加热"));
            steps.add(createStep("放入鸡胸肉，每面煎3-4分钟至金黄"));
            steps.add(createStep("加入蔬菜翻炒2分钟，调味即可"));
            recipe.setSteps(steps);
            
        } else if (prompt.contains("牛肉") || prompt.contains("增肌")) {
            recipe.setTitle("黑椒牛肉炒饭");
            recipe.setDescription("高蛋白增肌餐，牛肉嫩滑，营养丰富");
            
            List<AiRecipeData.IngredientItem> ingredients = new ArrayList<>();
            ingredients.add(createIngredient("牛肉", "200g"));
            ingredients.add(createIngredient("米饭", "200g"));
            ingredients.add(createIngredient("鸡蛋", "2个"));
            ingredients.add(createIngredient("洋葱", "50g"));
            ingredients.add(createIngredient("青椒", "50g"));
            ingredients.add(createIngredient("生抽", "15ml"));
            ingredients.add(createIngredient("黑胡椒", "适量"));
            recipe.setIngredients(ingredients);
            
            List<AiRecipeData.StepItem> steps = new ArrayList<>();
            steps.add(createStep("牛肉切丝，用生抽和黑胡椒腌制10分钟"));
            steps.add(createStep("鸡蛋打散炒熟盛起，洋葱青椒切丝"));
            steps.add(createStep("热锅下油，爆炒牛肉丝至变色"));
            steps.add(createStep("加入洋葱青椒炒香"));
            steps.add(createStep("倒入米饭和鸡蛋炒匀，调味即可"));
            recipe.setSteps(steps);
            
        } else if (prompt.contains("素食") || prompt.contains("蔬菜")) {
            recipe.setTitle("彩虹蔬菜沙拉");
            recipe.setDescription("清爽健康的素食沙拉，富含维生素和纤维");
            
            List<AiRecipeData.IngredientItem> ingredients = new ArrayList<>();
            ingredients.add(createIngredient("生菜", "100g"));
            ingredients.add(createIngredient("番茄", "100g"));
            ingredients.add(createIngredient("黄瓜", "100g"));
            ingredients.add(createIngredient("胡萝卜", "50g"));
            ingredients.add(createIngredient("紫甘蓝", "50g"));
            ingredients.add(createIngredient("橄榄油", "15ml"));
            ingredients.add(createIngredient("柠檬汁", "10ml"));
            recipe.setIngredients(ingredients);
            
            List<AiRecipeData.StepItem> steps = new ArrayList<>();
            steps.add(createStep("所有蔬菜洗净，生菜撕成小片"));
            steps.add(createStep("番茄、黄瓜切块，胡萝卜切丝"));
            steps.add(createStep("紫甘蓝切丝，所有蔬菜混合"));
            steps.add(createStep("橄榄油和柠檬汁调成沙拉汁"));
            steps.add(createStep("淋上沙拉汁拌匀即可"));
            recipe.setSteps(steps);
            
        } else {
            // 默认菜谱
            recipe.setTitle("家常炒蛋");
            recipe.setDescription("简单易做的家常菜，营养美味");
            
            List<AiRecipeData.IngredientItem> ingredients = new ArrayList<>();
            ingredients.add(createIngredient("鸡蛋", "3个"));
            ingredients.add(createIngredient("韭菜", "100g"));
            ingredients.add(createIngredient("盐", "适量"));
            ingredients.add(createIngredient("食用油", "15ml"));
            recipe.setIngredients(ingredients);
            
            List<AiRecipeData.StepItem> steps = new ArrayList<>();
            steps.add(createStep("鸡蛋打散，加盐调味"));
            steps.add(createStep("韭菜洗净切段"));
            steps.add(createStep("热锅下油，倒入蛋液炒熟盛起"));
            steps.add(createStep("锅中留底油，下韭菜炒香"));
            steps.add(createStep("倒入炒蛋拌匀即可"));
            recipe.setSteps(steps);
        }
        
        return recipe;
    }
    
    private AiRecipeData.IngredientItem createIngredient(String name, String quantity) {
        AiRecipeData.IngredientItem ingredient = new AiRecipeData.IngredientItem();
        ingredient.setName(name);
        ingredient.setQuantity(quantity);
        return ingredient;
    }
    
    private AiRecipeData.StepItem createStep(String description) {
        AiRecipeData.StepItem step = new AiRecipeData.StepItem();
        step.setDescription(description);
        return step;
    }
}
