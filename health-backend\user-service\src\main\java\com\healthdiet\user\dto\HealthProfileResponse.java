package com.healthdiet.user.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 健康档案响应DTO
 */
@Data
public class HealthProfileResponse {
    
    private Long userId;
    private Integer gender;
    private String genderText;
    private Integer age;
    private BigDecimal height;
    private BigDecimal weight;
    private Integer activityLevel;
    private String activityLevelText;
    private Integer goal;
    private String goalText;
    private Integer targetCalories;
    private BigDecimal bmi;
    private String bmiCategory;
    
    /**
     * 设置性别文本
     */
    public void setGenderText() {
        if (gender != null) {
            this.genderText = gender == 0 ? "女" : "男";
        }
    }
    
    /**
     * 设置运动频率文本
     */
    public void setActivityLevelText() {
        if (activityLevel != null) {
            switch (activityLevel) {
                case 1 -> this.activityLevelText = "基本不动";
                case 2 -> this.activityLevelText = "每周1-3次";
                case 3 -> this.activityLevelText = "每周3-5次";
                case 4 -> this.activityLevelText = "每周6-7次";
                default -> this.activityLevelText = "未知";
            }
        }
    }
    
    /**
     * 设置健康目标文本
     */
    public void setGoalText() {
        if (goal != null) {
            switch (goal) {
                case 1 -> this.goalText = "减脂";
                case 2 -> this.goalText = "保持体重";
                case 3 -> this.goalText = "增肌";
                default -> this.goalText = "未知";
            }
        }
    }
    
    /**
     * 计算并设置BMI
     */
    public void calculateBmi() {
        if (height != null && weight != null && height.compareTo(BigDecimal.ZERO) > 0) {
            // BMI = 体重(kg) / (身高(m))^2
            BigDecimal heightInMeters = height.divide(new BigDecimal("100"), 2, java.math.RoundingMode.HALF_UP);
            this.bmi = weight.divide(heightInMeters.multiply(heightInMeters), 1, java.math.RoundingMode.HALF_UP);
            
            // 设置BMI分类
            if (bmi.compareTo(new BigDecimal("18.5")) < 0) {
                this.bmiCategory = "偏瘦";
            } else if (bmi.compareTo(new BigDecimal("24")) < 0) {
                this.bmiCategory = "正常";
            } else if (bmi.compareTo(new BigDecimal("28")) < 0) {
                this.bmiCategory = "超重";
            } else {
                this.bmiCategory = "肥胖";
            }
        }
    }
}
