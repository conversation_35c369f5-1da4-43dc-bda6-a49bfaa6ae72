import request from './request'
import type { 
  Recipe, 
  RecipeSearchParams, 
  RecipeCreateRequest, 
  AiRecipeRequest 
} from '@/types/recipe'

// 搜索菜谱
export function searchRecipes(params: RecipeSearchParams) {
  return request.get<{
    content: Recipe[]
    totalElements: number
    totalPages: number
    number: number
    size: number
  }>('/recipes/search', { params })
}

// 获取菜谱详情
export function getRecipeDetail(id: number) {
  return request.get<Recipe>(`/recipes/${id}`)
}

// 创建菜谱
export function createRecipe(data: RecipeCreateRequest) {
  return request.post<number>('/recipes', data)
}

// 更新菜谱
export function updateRecipe(id: number, data: Partial<RecipeCreateRequest>) {
  return request.put<Recipe>(`/recipes/${id}`, data)
}

// 删除菜谱
export function deleteRecipe(id: number) {
  return request.delete(`/recipes/${id}`)
}

// 收藏菜谱
export function favoriteRecipe(recipeId: number) {
  return request.post(`/recipes/${recipeId}/favorite`)
}

// 取消收藏菜谱
export function unfavoriteRecipe(recipeId: number) {
  return request.delete(`/recipes/${recipeId}/favorite`)
}

// 获取我的收藏
export function getMyFavorites(page = 0, size = 10) {
  return request.get<{
    content: Recipe[]
    totalElements: number
    totalPages: number
    number: number
    size: number
  }>('/recipes/favorites', {
    params: { page, size }
  })
}

// AI生成菜谱
export function generateAiRecipe(data: AiRecipeRequest) {
  return request.post<string>('/aigc/generate-recipe', data)
}

// 获取AI生成任务状态
export function getAiTaskStatus(taskId: string) {
  return request.get<{
    status: string
    result?: any
    error?: string
  }>(`/aigc/task/${taskId}`)
}

// 上传图片
export function uploadImage(file: File) {
  const formData = new FormData()
  formData.append('file', file)
  return request.post<string>('/recipes/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
