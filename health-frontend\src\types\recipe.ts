export interface Recipe {
  id: number
  title: string
  description: string
  coverImageUrl?: string
  cookingTime: number
  difficulty: number // 1-简单，2-中等，3-困难
  servings: number
  estimatedCalories?: number
  createdBy: number
  isAiGenerated: boolean
  createdAt: string
  updatedAt: string
  ingredients?: RecipeIngredient[]
  steps?: RecipeStep[]
  isFavorited?: boolean
}

export interface RecipeIngredient {
  id: number
  recipeId: number
  ingredientName: string
  quantity: string
}

export interface RecipeStep {
  id: number
  recipeId: number
  stepNumber: number
  instruction: string
  imageUrl?: string
}

export interface RecipeSearchParams {
  keyword?: string
  difficulty?: number
  maxCookingTime?: number
  page?: number
  size?: number
}

export interface RecipeCreateRequest {
  title: string
  description: string
  coverImageUrl?: string
  cookingTime: number
  difficulty: number
  servings: number
  ingredients: {
    ingredientName: string
    quantity: string
  }[]
  steps: {
    stepNumber: number
    instruction: string
    imageUrl?: string
  }[]
}

export interface AiRecipeRequest {
  preferences: string
  dietaryRestrictions?: string
  cookingTime?: number
  servings?: number
}
