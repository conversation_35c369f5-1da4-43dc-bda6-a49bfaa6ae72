<template>
  <MainLayout>
    <div class="recipe-list-container">
      <div class="search-section">
        <el-card>
          <el-form :model="searchForm" inline>
            <el-form-item label="关键词">
              <el-input
                v-model="searchForm.keyword"
                placeholder="搜索菜谱名称或描述"
                clearable
                @keyup.enter="handleSearch"
              />
            </el-form-item>
            <el-form-item label="难度">
              <el-select v-model="searchForm.difficulty" placeholder="选择难度" clearable>
                <el-option label="简单" :value="1" />
                <el-option label="中等" :value="2" />
                <el-option label="困难" :value="3" />
              </el-select>
            </el-form-item>
            <el-form-item label="烹饪时间">
              <el-input-number
                v-model="searchForm.maxCookingTime"
                placeholder="最大烹饪时间(分钟)"
                :min="1"
                :max="300"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch" :loading="loading">
                <el-icon><Search /></el-icon>
                搜索
              </el-button>
              <el-button @click="handleReset">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>

      <div class="recipe-grid">
        <el-row :gutter="24">
          <el-col :span="8" v-for="recipe in recipes" :key="recipe.id">
            <el-card class="recipe-card" @click="viewRecipe(recipe.id)">
              <div class="recipe-image">
                <img :src="recipe.coverImageUrl || '/default-recipe.jpg'" :alt="recipe.title" />
                <div class="recipe-overlay">
                  <el-button
                    type="primary"
                    circle
                    @click.stop="toggleFavorite(recipe)"
                    :icon="recipe.isFavorited ? 'StarFilled' : 'Star'"
                  />
                </div>
              </div>
              <div class="recipe-info">
                <h3>{{ recipe.title }}</h3>
                <p>{{ recipe.description }}</p>
                <div class="recipe-meta">
                  <span><el-icon><Timer /></el-icon> {{ recipe.cookingTime }}分钟</span>
                  <span><el-icon><Star /></el-icon> {{ recipe.estimatedCalories }}卡</span>
                  <span class="difficulty">{{ getDifficultyText(recipe.difficulty) }}</span>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <div class="pagination-section" v-if="total > 0">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[12, 24, 48]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </MainLayout>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import MainLayout from '@/components/Layout/MainLayout.vue'
import { searchRecipes, favoriteRecipe, unfavoriteRecipe } from '@/api/recipe'
import type { Recipe, RecipeSearchParams } from '@/types/recipe'

const router = useRouter()

const loading = ref(false)
const recipes = ref<Recipe[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(12)

const searchForm = reactive<RecipeSearchParams>({
  keyword: '',
  difficulty: undefined,
  maxCookingTime: undefined
})

const loadRecipes = async () => {
  try {
    loading.value = true
    const params: RecipeSearchParams = {
      ...searchForm,
      page: currentPage.value - 1,
      size: pageSize.value
    }
    
    const result = await searchRecipes(params)
    recipes.value = result.content
    total.value = result.totalElements
  } catch (error) {
    console.error('加载菜谱失败:', error)
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  currentPage.value = 1
  loadRecipes()
}

const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    difficulty: undefined,
    maxCookingTime: undefined
  })
  handleSearch()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  loadRecipes()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadRecipes()
}

const viewRecipe = (id: number) => {
  router.push(`/recipes/${id}`)
}

const toggleFavorite = async (recipe: Recipe) => {
  try {
    if (recipe.isFavorited) {
      await unfavoriteRecipe(recipe.id)
      recipe.isFavorited = false
      ElMessage.success('已取消收藏')
    } else {
      await favoriteRecipe(recipe.id)
      recipe.isFavorited = true
      ElMessage.success('收藏成功')
    }
  } catch (error) {
    console.error('操作失败:', error)
  }
}

const getDifficultyText = (difficulty: number) => {
  const map = { 1: '简单', 2: '中等', 3: '困难' }
  return map[difficulty as keyof typeof map] || '未知'
}

onMounted(() => {
  loadRecipes()
})
</script>

<style scoped>
.recipe-list-container {
  max-width: 1200px;
  margin: 0 auto;
}

.search-section {
  margin-bottom: 24px;
}

.recipe-grid {
  margin-bottom: 24px;
}

.recipe-card {
  cursor: pointer;
  transition: all 0.3s;
  margin-bottom: 24px;
  height: 320px;
}

.recipe-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.recipe-image {
  position: relative;
  height: 180px;
  overflow: hidden;
  border-radius: 8px;
  margin-bottom: 12px;
}

.recipe-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.recipe-overlay {
  position: absolute;
  top: 8px;
  right: 8px;
  opacity: 0;
  transition: opacity 0.3s;
}

.recipe-card:hover .recipe-overlay {
  opacity: 1;
}

.recipe-info h3 {
  margin-bottom: 8px;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.recipe-info p {
  color: #666;
  font-size: 14px;
  margin-bottom: 12px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.recipe-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #999;
}

.recipe-meta span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.difficulty {
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 4px;
}

.pagination-section {
  display: flex;
  justify-content: center;
  margin-top: 32px;
}
</style>
