package com.healthdiet.recipe.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.healthdiet.common.result.PageResult;
import com.healthdiet.common.result.Result;
import com.healthdiet.recipe.dto.AiRecipeData;
import com.healthdiet.recipe.dto.RecipeListResponse;
import com.healthdiet.recipe.dto.RecipeRequest;
import com.healthdiet.recipe.dto.RecipeResponse;
import com.healthdiet.recipe.service.RecipeService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 菜谱控制器
 */
@RestController
@RequestMapping("/recipes")
@RequiredArgsConstructor
public class RecipeController {
    
    private final RecipeService recipeService;
    
    /**
     * 创建菜谱
     */
    @PostMapping
    public Result<Long> createRecipe(
            @RequestHeader("X-User-Id") Long userId,
            @Valid @RequestBody RecipeRequest request) {
        try {
            Long recipeId = recipeService.createRecipe(userId, request);
            return Result.success("菜谱创建成功", recipeId);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 更新菜谱
     */
    @PutMapping("/{recipeId}")
    public Result<Void> updateRecipe(
            @PathVariable Long recipeId,
            @RequestHeader("X-User-Id") Long userId,
            @Valid @RequestBody RecipeRequest request) {
        try {
            recipeService.updateRecipe(recipeId, userId, request);
            return Result.success("菜谱更新成功", null);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 删除菜谱
     */
    @DeleteMapping("/{recipeId}")
    public Result<Void> deleteRecipe(
            @PathVariable Long recipeId,
            @RequestHeader("X-User-Id") Long userId) {
        try {
            recipeService.deleteRecipe(recipeId, userId);
            return Result.success("菜谱删除成功", null);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 获取菜谱详情
     */
    @GetMapping("/{recipeId}")
    public Result<RecipeResponse> getRecipeDetail(
            @PathVariable Long recipeId,
            @RequestHeader(value = "X-User-Id", required = false) Long currentUserId) {
        try {
            RecipeResponse response = recipeService.getRecipeDetail(recipeId, currentUserId);
            return Result.success(response);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 分页获取菜谱列表
     */
    @GetMapping
    public Result<PageResult<RecipeListResponse>> getRecipeList(
            @RequestParam(defaultValue = "1") Long currentPage,
            @RequestParam(defaultValue = "10") Long pageSize,
            @RequestParam(required = false) String keyword,
            @RequestHeader(value = "X-User-Id", required = false) Long currentUserId) {
        try {
            IPage<RecipeListResponse> page = recipeService.getRecipeList(currentPage, pageSize, keyword, currentUserId);
            PageResult<RecipeListResponse> result = new PageResult<>(
                page.getRecords(), page.getTotal(), page.getCurrent(), page.getSize());
            return Result.success(result);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 获取用户发布的菜谱
     */
    @GetMapping("/user/{userId}")
    public Result<PageResult<RecipeListResponse>> getUserRecipes(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "1") Long currentPage,
            @RequestParam(defaultValue = "10") Long pageSize,
            @RequestHeader(value = "X-User-Id", required = false) Long currentUserId) {
        try {
            IPage<RecipeListResponse> page = recipeService.getUserRecipes(userId, currentPage, pageSize, currentUserId);
            PageResult<RecipeListResponse> result = new PageResult<>(
                page.getRecords(), page.getTotal(), page.getCurrent(), page.getSize());
            return Result.success(result);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 收藏/取消收藏菜谱
     */
    @PostMapping("/{recipeId}/favorite")
    public Result<Void> toggleFavorite(
            @PathVariable Long recipeId,
            @RequestHeader("X-User-Id") Long userId) {
        try {
            recipeService.toggleFavorite(recipeId, userId);
            return Result.success("操作成功", null);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 获取用户收藏的菜谱
     */
    @GetMapping("/favorites")
    public Result<PageResult<RecipeListResponse>> getFavoriteRecipes(
            @RequestHeader("X-User-Id") Long userId,
            @RequestParam(defaultValue = "1") Long currentPage,
            @RequestParam(defaultValue = "10") Long pageSize) {
        try {
            IPage<RecipeListResponse> page = recipeService.getFavoriteRecipes(userId, currentPage, pageSize);
            PageResult<RecipeListResponse> result = new PageResult<>(
                page.getRecords(), page.getTotal(), page.getCurrent(), page.getSize());
            return Result.success(result);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 创建AI生成的菜谱
     */
    @PostMapping("/ai")
    public Result<Long> createAiRecipe(
            @RequestHeader("X-User-Id") Long userId,
            @RequestBody AiRecipeData aiRecipeData) {
        try {
            Long recipeId = recipeService.createAiRecipe(userId, aiRecipeData);
            return Result.success("AI菜谱创建成功", recipeId);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取菜谱食材列表
     */
    @GetMapping("/{recipeId}/ingredients")
    public Result<java.util.List<RecipeIngredientResponse>> getRecipeIngredients(@PathVariable Long recipeId) {
        try {
            java.util.List<RecipeIngredientResponse> ingredients = recipeService.getRecipeIngredients(recipeId);
            return Result.success(ingredients);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 菜谱食材响应DTO
     */
    public static class RecipeIngredientResponse {
        private String ingredientName;
        private String quantity;

        public RecipeIngredientResponse() {}

        public RecipeIngredientResponse(String ingredientName, String quantity) {
            this.ingredientName = ingredientName;
            this.quantity = quantity;
        }

        // Getters and Setters
        public String getIngredientName() { return ingredientName; }
        public void setIngredientName(String ingredientName) { this.ingredientName = ingredientName; }

        public String getQuantity() { return quantity; }
        public void setQuantity(String quantity) { this.quantity = quantity; }
    }
}
