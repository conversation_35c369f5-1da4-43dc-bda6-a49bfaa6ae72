package com.healthdiet.plan.dto;

import lombok.Data;

import java.time.LocalDate;

/**
 * 餐食计划响应DTO
 */
@Data
public class MealPlanResponse {
    
    /**
     * 计划ID
     */
    private Long id;
    
    /**
     * 菜谱ID
     */
    private Long recipeId;
    
    /**
     * 菜谱名称
     */
    private String recipeTitle;
    
    /**
     * 菜谱封面图
     */
    private String recipeCoverImage;
    
    /**
     * 菜谱热量
     */
    private Integer recipeCalories;
    
    /**
     * 计划日期
     */
    private LocalDate planDate;
    
    /**
     * 餐别类型：1-早餐，2-午餐，3-晚餐
     */
    private Integer mealType;
    
    /**
     * 餐别名称
     */
    private String mealTypeName;
}
