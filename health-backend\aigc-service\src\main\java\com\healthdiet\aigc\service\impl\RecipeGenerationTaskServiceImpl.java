package com.healthdiet.aigc.service.impl;

import com.healthdiet.aigc.dto.AiRecipeData;
import com.healthdiet.aigc.dto.RecipeGenerationRequest;
import com.healthdiet.aigc.service.AiModelService;
import com.healthdiet.aigc.service.RecipeGenerationTaskService;
import com.healthdiet.aigc.service.RecipeServiceClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 菜谱生成任务服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RecipeGenerationTaskServiceImpl implements RecipeGenerationTaskService {
    
    private final RabbitTemplate rabbitTemplate;
    private final AiModelService aiModelService;
    private final RecipeServiceClient recipeServiceClient;
    private final AigcServiceImpl aigcService;
    
    private static final String TASK_QUEUE = "recipe.generation.queue";
    
    @Override
    public void sendGenerationTask(String taskId, RecipeGenerationRequest request) {
        Map<String, Object> message = new HashMap<>();
        message.put("taskId", taskId);
        message.put("request", request);
        
        rabbitTemplate.convertAndSend(TASK_QUEUE, message);
        log.info("菜谱生成任务已发送到队列，任务ID: {}", taskId);
    }
    
    @RabbitListener(queues = TASK_QUEUE)
    public void processGenerationTask(Map<String, Object> message) {
        String taskId = (String) message.get("taskId");
        RecipeGenerationRequest request = (RecipeGenerationRequest) message.get("request");
        
        log.info("开始处理菜谱生成任务，任务ID: {}", taskId);
        
        try {
            // 更新任务状态为处理中
            aigcService.updateTaskStatus(taskId, "PROCESSING", null, null);
            
            // 调用AI模型生成菜谱
            AiRecipeData aiRecipeData = aiModelService.generateRecipe(request);
            
            // 调用菜谱服务保存菜谱
            Long recipeId = recipeServiceClient.createAiRecipe(request.getUserId(), aiRecipeData);
            
            // 更新任务状态为完成
            aigcService.updateTaskStatus(taskId, "COMPLETED", recipeId, null);
            
            log.info("菜谱生成任务完成，任务ID: {}, 菜谱ID: {}", taskId, recipeId);
            
        } catch (Exception e) {
            log.error("菜谱生成任务失败，任务ID: {}", taskId, e);
            aigcService.updateTaskStatus(taskId, "FAILED", null, e.getMessage());
        }
    }
    
    @Override
    public void processGenerationTask(String taskId, RecipeGenerationRequest request) {
        // 这个方法由RabbitListener调用，不需要手动实现
    }
}
