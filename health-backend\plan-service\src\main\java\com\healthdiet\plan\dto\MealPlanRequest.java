package com.healthdiet.plan.dto;

import lombok.Data;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import java.time.LocalDate;

/**
 * 餐食计划请求DTO
 */
@Data
public class MealPlanRequest {
    
    /**
     * 菜谱ID
     */
    @NotNull(message = "菜谱ID不能为空")
    private Long recipeId;
    
    /**
     * 计划日期
     */
    @NotNull(message = "计划日期不能为空")
    private LocalDate planDate;
    
    /**
     * 餐别类型：1-早餐，2-午餐，3-晚餐
     */
    @NotNull(message = "餐别类型不能为空")
    @Min(value = 1, message = "餐别类型必须在1-3之间")
    @Max(value = 3, message = "餐别类型必须在1-3之间")
    private Integer mealType;
}
