package com.healthdiet.auth.service;

import com.healthdiet.auth.dto.LoginRequest;
import com.healthdiet.auth.dto.LoginResponse;
import com.healthdiet.auth.dto.RegisterRequest;

/**
 * 认证服务接口
 */
public interface AuthService {
    
    /**
     * 用户注册
     */
    void register(RegisterRequest request);
    
    /**
     * 用户登录
     */
    LoginResponse login(LoginRequest request);
    
    /**
     * 验证Token
     */
    boolean validateToken(String token);
    
    /**
     * 从Token获取用户ID
     */
    Long getUserIdFromToken(String token);
}
