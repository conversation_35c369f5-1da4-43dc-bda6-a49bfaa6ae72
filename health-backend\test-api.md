# API测试指南

## 测试环境准备

### 1. 启动基础设施
在测试之前，需要启动以下基础设施：

```bash
# 启动MySQL (需要预先安装)
# 创建数据库并执行初始化脚本
mysql -u root -p < sql/init.sql

# 启动Redis (需要预先安装)
redis-server

# 启动Nacos (需要预先下载)
# 下载地址: https://github.com/alibaba/nacos/releases
# 启动命令: startup.cmd -m standalone
```

### 2. 启动微服务
按以下顺序启动服务：

```bash
# 1. 启动网关服务
cd health-backend/gateway-service
mvn spring-boot:run

# 2. 启动认证服务
cd health-backend/auth-service  
mvn spring-boot:run

# 3. 启动用户服务
cd health-backend/user-service
mvn spring-boot:run
```

## API测试用例

### 1. 认证服务测试

#### 用户注册
```bash
curl -X POST http://localhost:8080/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "123456"
  }'
```

#### 用户登录
```bash
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser", 
    "password": "123456"
  }'
```

#### Token验证
```bash
curl -X GET "http://localhost:8080/api/auth/validate?token=YOUR_TOKEN_HERE"
```

### 2. 用户服务测试

#### 创建健康档案
```bash
curl -X POST http://localhost:8080/api/users/health-profile/1 \
  -H "Content-Type: application/json" \
  -d '{
    "gender": 1,
    "age": 25,
    "height": 175.0,
    "weight": 70.0,
    "activityLevel": 2,
    "goal": 1
  }'
```

#### 获取健康档案
```bash
curl -X GET http://localhost:8080/api/users/health-profile/1
```

## 预期响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 具体数据
  }
}
```

### 错误响应
```json
{
  "code": 500,
  "message": "错误信息"
}
```

## 测试检查点

- [ ] 所有服务能正常启动
- [ ] 用户注册功能正常
- [ ] 用户登录功能正常，返回有效Token
- [ ] Token验证功能正常
- [ ] 健康档案创建功能正常
- [ ] 健康档案查询功能正常，包含计算的BMI和目标卡路里
- [ ] API网关路由正常工作
- [ ] 错误处理正常，返回合适的错误码和信息

## 故障排除

### 常见问题
1. **服务启动失败**: 检查数据库连接配置
2. **Nacos连接失败**: 确保Nacos服务已启动
3. **端口冲突**: 检查端口是否被占用
4. **数据库连接失败**: 检查MySQL服务状态和连接参数

### 日志查看
各服务的日志会输出到控制台，注意查看错误信息进行调试。
