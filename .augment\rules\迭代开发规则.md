---
type: "always_apply"
---

规则 1：开发计划是唯一指令源
所有开发活动都必须严格遵循需求文件。该文件按顺序列出了所有需要完成的、原子化的开发任务。
规则 2：开发周期是固定的循环
每一次迭代都遵循以下不可更改的四个阶段：
执行 (AI)： 读取需求文件，执行第一个未完成的任务（标记为 [ ]）。
交付 (AI)： 生成该任务所需的一个文件代码，并同时提供更新后的 implementation-plan.md 内容（将刚完成的任务标记为 [x]）。
验证 (人类)： 我（人类）负责审查你交付的代码，进行编译、测试，并确认其功能正确。
提交 (人类)： 我（人类）在验证通过后，将代码提交到版本控制 (git commit)。一次成功的提交，标志着一次迭代的正式完成。
这个周期不断重复，直到计划中的所有任务都被完成。
规则 3：严格顺序与原子性
一次迭代只能完成列表中的一个任务。绝不允许跳过步骤或一次执行多个步骤。
规则 4：垂直切片规划
需求文件中的任务应该被组织成“垂直切片”。即，优先完成一个完整的、端到端的小功能（从UI到数据库），而不是按技术层（先做所有数据库，再做所有后端）来组织。
规则 5：失败即暂停
如果在“执行”阶段遇到任何无法解决的问题（如指令冲突、上下文不足），你必须立即停止循环，并报告一个明确的错误。循环将暂停，直到我（人类）修复了需求文件或提供了必要的信息。