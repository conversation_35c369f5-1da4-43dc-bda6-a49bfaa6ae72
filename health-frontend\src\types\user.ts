export interface User {
  id: number
  username: string
  email: string
  nickname?: string
  avatar?: string
  createdAt: string
  updatedAt: string
}

export interface UserProfile {
  id: number
  userId: number
  gender: number // 1-男，2-女
  age: number
  height: number // 身高(cm)
  weight: number // 体重(kg)
  activityLevel: number // 运动频率：1-久坐，2-轻度活动，3-中度活动，4-重度活动，5-极重度活动
  healthGoal: number // 健康目标：1-减重，2-增重，3-保持，4-增肌，5-塑形
  bmr: number // 基础代谢率
  tdee: number // 每日总能量消耗
  targetCalories: number // 目标卡路里
  createdAt: string
  updatedAt: string
}

export interface LoginRequest {
  username: string
  password: string
}

export interface RegisterRequest {
  username: string
  email: string
  password: string
  confirmPassword: string
}

export interface UserProfileRequest {
  gender: number
  age: number
  height: number
  weight: number
  activityLevel: number
  healthGoal: number
}
