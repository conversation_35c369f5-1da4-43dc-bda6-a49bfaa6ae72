package com.healthdiet.recipe.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 食材实体类
 */
@Data
@TableName("ingredients")
public class Ingredient {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 食材名称
     */
    private String name;
    
    /**
     * 每100g的卡路里
     */
    private Integer caloriesPer100g;
    
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
}
