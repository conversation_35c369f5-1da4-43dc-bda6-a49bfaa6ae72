@echo off
echo ========================================
echo 健康饮食推荐平台 - 停止所有服务
echo ========================================

echo.
echo [1/2] 停止Spring Boot微服务...
for /f "tokens=1" %%i in ('jps -l ^| findstr "org.springframework.boot.loader"') do (
    echo 停止进程 %%i
    taskkill /F /PID %%i
)

echo.
echo [2/2] 停止Docker容器...
docker-compose down

echo.
echo ========================================
echo 所有服务已停止
echo ========================================

pause
