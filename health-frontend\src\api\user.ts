import request from './request'
import type { 
  User, 
  UserProfile, 
  LoginRequest, 
  RegisterRequest, 
  UserProfileRequest 
} from '@/types/user'

// 用户登录
export function login(data: LoginRequest) {
  return request.post<string>('/auth/login', data)
}

// 用户注册
export function register(data: RegisterRequest) {
  return request.post<string>('/auth/register', data)
}

// 获取用户信息
export function getUserInfo() {
  return request.get<User>('/users/info')
}

// 更新用户信息
export function updateUserInfo(data: Partial<User>) {
  return request.put<User>('/users/info', data)
}

// 获取用户健康档案
export function getUserProfile() {
  return request.get<UserProfile>('/users/profile')
}

// 创建或更新用户健康档案
export function saveUserProfile(data: UserProfileRequest) {
  return request.post<UserProfile>('/users/profile', data)
}

// 上传头像
export function uploadAvatar(file: File) {
  const formData = new FormData()
  formData.append('file', file)
  return request.post<string>('/users/avatar', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
