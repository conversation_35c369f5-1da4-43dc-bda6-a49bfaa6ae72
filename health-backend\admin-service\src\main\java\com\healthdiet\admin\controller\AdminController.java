package com.healthdiet.admin.controller;

import com.healthdiet.admin.dto.AdminLoginRequest;
import com.healthdiet.admin.dto.AdminResponse;
import com.healthdiet.admin.dto.DashboardStatsResponse;
import com.healthdiet.admin.service.AdminService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 管理员控制器
 */
@RestController
@RequestMapping("/api/admin")
@RequiredArgsConstructor
public class AdminController {
    
    private final AdminService adminService;
    
    /**
     * 管理员登录
     */
    @PostMapping("/login")
    public ResponseEntity<Result<String>> login(@Valid @RequestBody AdminLoginRequest request) {
        try {
            String token = adminService.login(request);
            return ResponseEntity.ok(Result.success("登录成功", token));
        } catch (Exception e) {
            return ResponseEntity.ok(Result.error(e.getMessage()));
        }
    }
    
    /**
     * 获取管理员信息
     */
    @GetMapping("/info")
    public ResponseEntity<Result<AdminResponse>> getAdminInfo(@RequestHeader("X-Admin-Id") Long adminId) {
        try {
            AdminResponse response = adminService.getAdminInfo(adminId);
            return ResponseEntity.ok(Result.success(response));
        } catch (Exception e) {
            return ResponseEntity.ok(Result.error(e.getMessage()));
        }
    }
    
    /**
     * 获取仪表板统计数据
     */
    @GetMapping("/dashboard/stats")
    public ResponseEntity<Result<DashboardStatsResponse>> getDashboardStats() {
        try {
            DashboardStatsResponse stats = adminService.getDashboardStats();
            return ResponseEntity.ok(Result.success(stats));
        } catch (Exception e) {
            return ResponseEntity.ok(Result.error(e.getMessage()));
        }
    }
    
    /**
     * 通用结果包装类
     */
    public static class Result<T> {
        private Integer code;
        private String message;
        private T data;
        
        public static <T> Result<T> success(T data) {
            Result<T> result = new Result<>();
            result.code = 200;
            result.message = "操作成功";
            result.data = data;
            return result;
        }
        
        public static <T> Result<T> success(String message, T data) {
            Result<T> result = new Result<>();
            result.code = 200;
            result.message = message;
            result.data = data;
            return result;
        }
        
        public static <T> Result<T> error(String message) {
            Result<T> result = new Result<>();
            result.code = 500;
            result.message = message;
            return result;
        }
        
        // Getters and Setters
        public Integer getCode() { return code; }
        public void setCode(Integer code) { this.code = code; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public T getData() { return data; }
        public void setData(T data) { this.data = data; }
    }
}
