package com.healthdiet.recipe.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import java.util.List;

/**
 * 菜谱创建/更新请求DTO
 */
@Data
public class RecipeRequest {
    
    @NotBlank(message = "菜谱名称不能为空")
    @Size(max = 255, message = "菜谱名称长度不能超过255个字符")
    private String title;
    
    @Size(max = 1000, message = "菜谱简介长度不能超过1000个字符")
    private String description;
    
    private String coverImageUrl;
    
    @NotEmpty(message = "食材列表不能为空")
    private List<IngredientItem> ingredients;
    
    @NotEmpty(message = "制作步骤不能为空")
    private List<StepItem> steps;
    
    @Data
    public static class IngredientItem {
        @NotBlank(message = "食材名称不能为空")
        private String name;
        
        @NotBlank(message = "食材用量不能为空")
        private String quantity;
    }
    
    @Data
    public static class StepItem {
        private Integer stepNumber;
        
        @NotBlank(message = "步骤描述不能为空")
        private String description;
        
        private String imageUrl;
    }
}
