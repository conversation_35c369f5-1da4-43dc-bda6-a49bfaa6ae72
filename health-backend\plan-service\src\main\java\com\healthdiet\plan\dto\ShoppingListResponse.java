package com.healthdiet.plan.dto;

import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * 购物清单响应DTO
 */
@Data
public class ShoppingListResponse {
    
    /**
     * 开始日期
     */
    private LocalDate startDate;
    
    /**
     * 结束日期
     */
    private LocalDate endDate;
    
    /**
     * 食材清单
     */
    private List<IngredientItem> ingredients;
    
    /**
     * 食材项
     */
    @Data
    public static class IngredientItem {
        /**
         * 食材名称
         */
        private String name;
        
        /**
         * 总用量
         */
        private String totalQuantity;
        
        /**
         * 单位
         */
        private String unit;
        
        /**
         * 是否已购买
         */
        private Boolean purchased = false;
    }
}
