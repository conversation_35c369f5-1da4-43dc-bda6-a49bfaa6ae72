<template>
  <MainLayout>
    <div class="meal-plan-container">
      <div class="plan-header">
        <h2>餐食计划</h2>
        <div class="week-navigation">
          <el-button @click="previousWeek" :icon="ArrowLeft">上一周</el-button>
          <span class="week-range">{{ weekRange }}</span>
          <el-button @click="nextWeek" :icon="ArrowRight">下一周</el-button>
        </div>
      </div>

      <div class="meal-plan-grid">
        <div
          v-for="(dayPlan, date) in weeklyPlan?.dailyPlans"
          :key="date"
          class="day-plan"
        >
          <div class="day-header">
            <h3>{{ formatDate(date) }}</h3>
            <span class="total-calories">{{ dayPlan.totalCalories }}卡</span>
          </div>
          
          <div class="meals">
            <!-- 早餐 -->
            <div class="meal-slot">
              <div class="meal-label">早餐</div>
              <div
                v-if="dayPlan.breakfast"
                class="meal-card"
                @click="viewRecipe(dayPlan.breakfast.recipeId)"
              >
                <img
                  :src="dayPlan.breakfast.recipeCoverImage || '/default-recipe.jpg'"
                  :alt="dayPlan.breakfast.recipeTitle"
                />
                <div class="meal-info">
                  <h4>{{ dayPlan.breakfast.recipeTitle }}</h4>
                  <span>{{ dayPlan.breakfast.recipeCalories }}卡</span>
                </div>
                <el-button
                  type="danger"
                  size="small"
                  circle
                  @click.stop="removeMeal(dayPlan.breakfast.id)"
                  :icon="Delete"
                />
              </div>
              <div v-else class="empty-meal" @click="addMeal(date, 1)">
                <el-icon><Plus /></el-icon>
                <span>添加早餐</span>
              </div>
            </div>

            <!-- 午餐 -->
            <div class="meal-slot">
              <div class="meal-label">午餐</div>
              <div
                v-if="dayPlan.lunch"
                class="meal-card"
                @click="viewRecipe(dayPlan.lunch.recipeId)"
              >
                <img
                  :src="dayPlan.lunch.recipeCoverImage || '/default-recipe.jpg'"
                  :alt="dayPlan.lunch.recipeTitle"
                />
                <div class="meal-info">
                  <h4>{{ dayPlan.lunch.recipeTitle }}</h4>
                  <span>{{ dayPlan.lunch.recipeCalories }}卡</span>
                </div>
                <el-button
                  type="danger"
                  size="small"
                  circle
                  @click.stop="removeMeal(dayPlan.lunch.id)"
                  :icon="Delete"
                />
              </div>
              <div v-else class="empty-meal" @click="addMeal(date, 2)">
                <el-icon><Plus /></el-icon>
                <span>添加午餐</span>
              </div>
            </div>

            <!-- 晚餐 -->
            <div class="meal-slot">
              <div class="meal-label">晚餐</div>
              <div
                v-if="dayPlan.dinner"
                class="meal-card"
                @click="viewRecipe(dayPlan.dinner.recipeId)"
              >
                <img
                  :src="dayPlan.dinner.recipeCoverImage || '/default-recipe.jpg'"
                  :alt="dayPlan.dinner.recipeTitle"
                />
                <div class="meal-info">
                  <h4>{{ dayPlan.dinner.recipeTitle }}</h4>
                  <span>{{ dayPlan.dinner.recipeCalories }}卡</span>
                </div>
                <el-button
                  type="danger"
                  size="small"
                  circle
                  @click.stop="removeMeal(dayPlan.dinner.id)"
                  :icon="Delete"
                />
              </div>
              <div v-else class="empty-meal" @click="addMeal(date, 3)">
                <el-icon><Plus /></el-icon>
                <span>添加晚餐</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="action-section">
        <el-button type="primary" @click="generateShoppingList" :loading="generatingList">
          <el-icon><ShoppingCart /></el-icon>
          生成购物清单
        </el-button>
      </div>
    </div>

    <!-- 添加餐食对话框 -->
    <el-dialog v-model="addMealDialogVisible" title="选择菜谱" width="800px">
      <div class="recipe-selection">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索菜谱"
          @input="searchRecipes"
          style="margin-bottom: 16px"
        />
        <div class="recipe-list">
          <div
            v-for="recipe in availableRecipes"
            :key="recipe.id"
            class="recipe-item"
            @click="selectRecipe(recipe)"
          >
            <img :src="recipe.coverImageUrl || '/default-recipe.jpg'" :alt="recipe.title" />
            <div class="recipe-details">
              <h4>{{ recipe.title }}</h4>
              <p>{{ recipe.description }}</p>
              <div class="recipe-stats">
                <span>{{ recipe.cookingTime }}分钟</span>
                <span>{{ recipe.estimatedCalories }}卡</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 购物清单对话框 -->
    <el-dialog v-model="shoppingListDialogVisible" title="购物清单" width="600px">
      <div v-if="shoppingList" class="shopping-list">
        <div class="list-header">
          <span>{{ shoppingList.startDate }} 至 {{ shoppingList.endDate }}</span>
        </div>
        <div class="ingredient-list">
          <div
            v-for="(ingredient, index) in shoppingList.ingredients"
            :key="index"
            class="ingredient-item"
          >
            <el-checkbox v-model="ingredient.purchased">
              {{ ingredient.name }} - {{ ingredient.totalQuantity }}
            </el-checkbox>
          </div>
        </div>
      </div>
    </el-dialog>
  </MainLayout>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, ArrowRight, Delete } from '@element-plus/icons-vue'
import MainLayout from '@/components/Layout/MainLayout.vue'
import { getWeeklyMealPlan, addMealPlan, removeMealPlan, generateShoppingList as generateShoppingListApi } from '@/api/plan'
import { searchRecipes as searchRecipesApi } from '@/api/recipe'
import type { WeeklyMealPlan, MealPlan, ShoppingList } from '@/types/plan'
import type { Recipe } from '@/types/recipe'

const router = useRouter()

const currentWeekStart = ref(new Date())
const weeklyPlan = ref<WeeklyMealPlan | null>(null)
const addMealDialogVisible = ref(false)
const shoppingListDialogVisible = ref(false)
const generatingList = ref(false)
const selectedDate = ref('')
const selectedMealType = ref(1)
const searchKeyword = ref('')
const availableRecipes = ref<Recipe[]>([])
const shoppingList = ref<ShoppingList | null>(null)

const weekRange = computed(() => {
  const start = new Date(currentWeekStart.value)
  const end = new Date(start)
  end.setDate(start.getDate() + 6)
  
  return `${formatDateShort(start)} - ${formatDateShort(end)}`
})

const loadWeeklyPlan = async () => {
  try {
    const weekStart = formatDateForApi(currentWeekStart.value)
    weeklyPlan.value = await getWeeklyMealPlan(weekStart)
  } catch (error) {
    console.error('加载餐食计划失败:', error)
  }
}

const previousWeek = () => {
  const newDate = new Date(currentWeekStart.value)
  newDate.setDate(newDate.getDate() - 7)
  currentWeekStart.value = newDate
  loadWeeklyPlan()
}

const nextWeek = () => {
  const newDate = new Date(currentWeekStart.value)
  newDate.setDate(newDate.getDate() + 7)
  currentWeekStart.value = newDate
  loadWeeklyPlan()
}

const addMeal = (date: string, mealType: number) => {
  selectedDate.value = date
  selectedMealType.value = mealType
  addMealDialogVisible.value = true
  searchRecipes()
}

const searchRecipes = async () => {
  try {
    const result = await searchRecipesApi({
      keyword: searchKeyword.value,
      page: 0,
      size: 20
    })
    availableRecipes.value = result.content
  } catch (error) {
    console.error('搜索菜谱失败:', error)
  }
}

const selectRecipe = async (recipe: Recipe) => {
  try {
    await addMealPlan({
      recipeId: recipe.id,
      planDate: selectedDate.value,
      mealType: selectedMealType.value
    })
    
    ElMessage.success('添加成功')
    addMealDialogVisible.value = false
    loadWeeklyPlan()
  } catch (error) {
    console.error('添加餐食失败:', error)
  }
}

const removeMeal = async (planId: number) => {
  try {
    await ElMessageBox.confirm('确定要移除这个餐食吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await removeMealPlan(planId)
    ElMessage.success('移除成功')
    loadWeeklyPlan()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('移除餐食失败:', error)
    }
  }
}

const viewRecipe = (recipeId: number) => {
  router.push(`/recipes/${recipeId}`)
}

const generateShoppingList = async () => {
  try {
    generatingList.value = true
    const startDate = formatDateForApi(currentWeekStart.value)
    const endDate = formatDateForApi(new Date(currentWeekStart.value.getTime() + 6 * 24 * 60 * 60 * 1000))
    
    shoppingList.value = await generateShoppingListApi({
      startDate,
      endDate
    })
    
    shoppingListDialogVisible.value = true
  } catch (error) {
    console.error('生成购物清单失败:', error)
  } finally {
    generatingList.value = false
  }
}

const formatDate = (dateStr: string) => {
  const date = new Date(dateStr)
  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
  return `${date.getMonth() + 1}/${date.getDate()} ${weekdays[date.getDay()]}`
}

const formatDateShort = (date: Date) => {
  return `${date.getMonth() + 1}/${date.getDate()}`
}

const formatDateForApi = (date: Date) => {
  return date.toISOString().split('T')[0]
}

// 获取本周开始日期（周一）
const getWeekStart = (date: Date) => {
  const d = new Date(date)
  const day = d.getDay()
  const diff = d.getDate() - day + (day === 0 ? -6 : 1)
  return new Date(d.setDate(diff))
}

onMounted(() => {
  currentWeekStart.value = getWeekStart(new Date())
  loadWeeklyPlan()
})
</script>

<style scoped>
.meal-plan-container {
  max-width: 1400px;
  margin: 0 auto;
}

.plan-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.week-navigation {
  display: flex;
  align-items: center;
  gap: 16px;
}

.week-range {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.meal-plan-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 16px;
  margin-bottom: 24px;
}

.day-plan {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.day-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.day-header h3 {
  margin: 0;
  font-size: 14px;
  color: #333;
}

.total-calories {
  font-size: 12px;
  color: #666;
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 4px;
}

.meals {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.meal-slot {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.meal-label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.meal-card {
  position: relative;
  background: #f8f9fa;
  border-radius: 6px;
  padding: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.meal-card:hover {
  background: #e9ecef;
}

.meal-card img {
  width: 100%;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
  margin-bottom: 8px;
}

.meal-info h4 {
  margin: 0 0 4px 0;
  font-size: 12px;
  color: #333;
  line-height: 1.2;
}

.meal-info span {
  font-size: 11px;
  color: #666;
}

.meal-card .el-button {
  position: absolute;
  top: 4px;
  right: 4px;
  opacity: 0;
  transition: opacity 0.3s;
}

.meal-card:hover .el-button {
  opacity: 1;
}

.empty-meal {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 80px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
  color: #999;
}

.empty-meal:hover {
  border-color: #409eff;
  color: #409eff;
}

.empty-meal span {
  font-size: 12px;
  margin-top: 4px;
}

.action-section {
  text-align: center;
}

.recipe-selection {
  max-height: 400px;
  overflow-y: auto;
}

.recipe-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.recipe-item {
  display: flex;
  gap: 12px;
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.recipe-item:hover {
  border-color: #409eff;
  background: #f0f8ff;
}

.recipe-item img {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 6px;
}

.recipe-details {
  flex: 1;
}

.recipe-details h4 {
  margin: 0 0 8px 0;
  color: #333;
}

.recipe-details p {
  margin: 0 0 8px 0;
  color: #666;
  font-size: 14px;
  line-height: 1.4;
}

.recipe-stats {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #999;
}

.shopping-list {
  max-height: 400px;
  overflow-y: auto;
}

.list-header {
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
  font-weight: 500;
  color: #333;
}

.ingredient-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.ingredient-item {
  padding: 8px 0;
}
</style>
