package com.healthdiet.user.service;

import com.healthdiet.user.dto.HealthProfileRequest;
import com.healthdiet.user.dto.HealthProfileResponse;

/**
 * 用户健康档案服务接口
 */
public interface UserHealthProfileService {
    
    /**
     * 创建或更新健康档案
     */
    void saveOrUpdateHealthProfile(Long userId, HealthProfileRequest request);
    
    /**
     * 获取用户健康档案
     */
    HealthProfileResponse getHealthProfile(Long userId);
    
    /**
     * 删除用户健康档案
     */
    void deleteHealthProfile(Long userId);
}
