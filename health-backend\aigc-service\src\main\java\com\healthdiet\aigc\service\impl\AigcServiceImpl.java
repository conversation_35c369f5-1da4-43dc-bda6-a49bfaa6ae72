package com.healthdiet.aigc.service.impl;

import com.healthdiet.aigc.dto.RecipeGenerationRequest;
import com.healthdiet.aigc.dto.RecipeGenerationResponse;
import com.healthdiet.aigc.service.AigcService;
import com.healthdiet.aigc.service.RecipeGenerationTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * AIGC服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AigcServiceImpl implements AigcService {
    
    private final RabbitTemplate rabbitTemplate;
    private final RedisTemplate<String, Object> redisTemplate;
    private final RecipeGenerationTaskService taskService;
    
    private static final String TASK_QUEUE = "recipe.generation.queue";
    private static final String TASK_STATUS_PREFIX = "task:status:";
    
    @Override
    public RecipeGenerationResponse submitRecipeGeneration(RecipeGenerationRequest request) {
        // 生成任务ID
        String taskId = UUID.randomUUID().toString();
        
        // 创建响应对象
        RecipeGenerationResponse response = new RecipeGenerationResponse();
        response.setTaskId(taskId);
        response.setStatus("PENDING");
        response.setCreatedAt(LocalDateTime.now());
        
        // 保存任务状态到Redis
        String statusKey = TASK_STATUS_PREFIX + taskId;
        redisTemplate.opsForValue().set(statusKey, response, 24, TimeUnit.HOURS);
        
        // 发送任务到消息队列
        try {
            taskService.sendGenerationTask(taskId, request);
            log.info("菜谱生成任务已提交，任务ID: {}", taskId);
        } catch (Exception e) {
            log.error("提交菜谱生成任务失败，任务ID: {}", taskId, e);
            response.setStatus("FAILED");
            response.setErrorMessage("任务提交失败: " + e.getMessage());
            redisTemplate.opsForValue().set(statusKey, response, 24, TimeUnit.HOURS);
        }
        
        return response;
    }
    
    @Override
    public RecipeGenerationResponse getTaskStatus(String taskId) {
        String statusKey = TASK_STATUS_PREFIX + taskId;
        RecipeGenerationResponse response = (RecipeGenerationResponse) redisTemplate.opsForValue().get(statusKey);
        
        if (response == null) {
            response = new RecipeGenerationResponse();
            response.setTaskId(taskId);
            response.setStatus("NOT_FOUND");
            response.setErrorMessage("任务不存在或已过期");
        }
        
        return response;
    }
    
    @Override
    public void cancelTask(String taskId) {
        String statusKey = TASK_STATUS_PREFIX + taskId;
        RecipeGenerationResponse response = (RecipeGenerationResponse) redisTemplate.opsForValue().get(statusKey);
        
        if (response != null && ("PENDING".equals(response.getStatus()) || "PROCESSING".equals(response.getStatus()))) {
            response.setStatus("CANCELLED");
            response.setCompletedAt(LocalDateTime.now());
            redisTemplate.opsForValue().set(statusKey, response, 24, TimeUnit.HOURS);
            log.info("任务已取消，任务ID: {}", taskId);
        }
    }
    
    /**
     * 更新任务状态（供内部调用）
     */
    public void updateTaskStatus(String taskId, String status, Long recipeId, String errorMessage) {
        String statusKey = TASK_STATUS_PREFIX + taskId;
        RecipeGenerationResponse response = (RecipeGenerationResponse) redisTemplate.opsForValue().get(statusKey);
        
        if (response != null) {
            response.setStatus(status);
            response.setRecipeId(recipeId);
            response.setErrorMessage(errorMessage);
            
            if ("COMPLETED".equals(status) || "FAILED".equals(status)) {
                response.setCompletedAt(LocalDateTime.now());
            }
            
            redisTemplate.opsForValue().set(statusKey, response, 24, TimeUnit.HOURS);
        }
    }
}
