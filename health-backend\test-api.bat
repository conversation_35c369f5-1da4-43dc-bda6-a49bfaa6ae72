@echo off
echo ========================================
echo 健康饮食推荐平台 - API功能测试
echo ========================================

echo.
echo 注意: 请确保所有服务已启动并运行正常
echo.

set BASE_URL=http://localhost:8080

echo [测试1] 用户注册...
curl -X POST %BASE_URL%/api/auth/register ^
  -H "Content-Type: application/json" ^
  -d "{\"username\":\"testuser\",\"password\":\"123456\"}"

echo.
echo.
echo [测试2] 用户登录...
curl -X POST %BASE_URL%/api/auth/login ^
  -H "Content-Type: application/json" ^
  -d "{\"username\":\"testuser\",\"password\":\"123456\"}" > login_response.json

echo.
echo.
echo [测试3] 创建健康档案...
curl -X POST %BASE_URL%/api/users/health-profile/1 ^
  -H "Content-Type: application/json" ^
  -d "{\"gender\":1,\"age\":25,\"height\":175.0,\"weight\":70.0,\"activityLevel\":2,\"goal\":1}"

echo.
echo.
echo [测试4] 获取健康档案...
curl -X GET %BASE_URL%/api/users/health-profile/1

echo.
echo.
echo [测试5] 导入营养数据...
curl -X POST %BASE_URL%/api/recipes/nutrition/import

echo.
echo.
echo [测试6] 创建菜谱...
curl -X POST %BASE_URL%/api/recipes ^
  -H "Content-Type: application/json" ^
  -H "X-User-Id: 1" ^
  -d "{\"title\":\"西兰花炒鸡胸肉\",\"description\":\"健康减脂餐\",\"ingredients\":[{\"name\":\"鸡胸肉\",\"quantity\":\"200g\"},{\"name\":\"西兰花\",\"quantity\":\"150g\"}],\"steps\":[{\"description\":\"鸡胸肉切块腌制\"},{\"description\":\"西兰花焯水\"},{\"description\":\"热锅炒制\"}]}"

echo.
echo.
echo [测试7] 获取菜谱列表...
curl -X GET %BASE_URL%/api/recipes

echo.
echo.
echo [测试8] AI生成菜谱...
curl -X POST %BASE_URL%/api/aigc/generate ^
  -H "Content-Type: application/json" ^
  -H "X-User-Id: 1" ^
  -d "{\"prompt\":\"用鸡胸肉和西兰花做一道减脂餐\"}"

echo.
echo.
echo ========================================
echo API测试完成
echo ========================================

echo.
echo 登录响应已保存到 login_response.json
echo 请检查各API的响应是否正常

pause
