package com.healthdiet.recipe.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.healthdiet.recipe.dto.AiRecipeData;
import com.healthdiet.recipe.dto.RecipeListResponse;
import com.healthdiet.recipe.dto.RecipeRequest;
import com.healthdiet.recipe.dto.RecipeResponse;

/**
 * 菜谱服务接口
 */
public interface RecipeService {
    
    /**
     * 创建菜谱
     */
    Long createRecipe(Long userId, RecipeRequest request);
    
    /**
     * 更新菜谱
     */
    void updateRecipe(Long recipeId, Long userId, RecipeRequest request);
    
    /**
     * 删除菜谱
     */
    void deleteRecipe(Long recipeId, Long userId);
    
    /**
     * 获取菜谱详情
     */
    RecipeResponse getRecipeDetail(Long recipeId, Long currentUserId);
    
    /**
     * 分页获取菜谱列表
     */
    IPage<RecipeListResponse> getRecipeList(Long currentPage, Long pageSize, String keyword, Long currentUserId);
    
    /**
     * 获取用户发布的菜谱
     */
    IPage<RecipeListResponse> getUserRecipes(Long userId, Long currentPage, Long pageSize, Long currentUserId);
    
    /**
     * 收藏/取消收藏菜谱
     */
    void toggleFavorite(Long recipeId, Long userId);
    
    /**
     * 获取用户收藏的菜谱
     */
    IPage<RecipeListResponse> getFavoriteRecipes(Long userId, Long currentPage, Long pageSize);

    /**
     * 创建AI生成的菜谱
     */
    Long createAiRecipe(Long userId, AiRecipeData aiRecipeData);

    /**
     * 获取菜谱食材列表
     */
    java.util.List<com.healthdiet.recipe.controller.RecipeController.RecipeIngredientResponse> getRecipeIngredients(Long recipeId);
}
