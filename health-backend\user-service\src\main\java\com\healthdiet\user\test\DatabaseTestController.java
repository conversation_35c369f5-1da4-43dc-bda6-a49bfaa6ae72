package com.healthdiet.user.test;

import com.healthdiet.common.result.Result;
import com.healthdiet.user.entity.User;
import com.healthdiet.user.mapper.UserMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 数据库测试控制器
 */
@RestController
@RequestMapping("/test/db")
@RequiredArgsConstructor
@Slf4j
public class DatabaseTestController {

    private final DataSource dataSource;
    private final UserMapper userMapper;
    private final RedisTemplate<String, Object> redisTemplate;

    /**
     * 测试数据库连接
     */
    @GetMapping("/connection")
    public Result<String> testConnection() {
        try (Connection connection = dataSource.getConnection()) {
            String url = connection.getMetaData().getURL();
            String username = connection.getMetaData().getUserName();
            log.info("数据库连接成功: URL={}, Username={}", url, username);
            return Result.success("数据库连接成功！URL: " + url + ", Username: " + username);
        } catch (SQLException e) {
            log.error("数据库连接失败", e);
            return Result.error("数据库连接失败: " + e.getMessage());
        }
    }

    /**
     * 测试Redis连接
     */
    @GetMapping("/redis")
    public Result<String> testRedis() {
        try {
            String testKey = "test:connection:" + System.currentTimeMillis();
            String testValue = "Hello Redis!";
            
            // 写入测试
            redisTemplate.opsForValue().set(testKey, testValue);
            
            // 读取测试
            String result = (String) redisTemplate.opsForValue().get(testKey);
            
            // 删除测试数据
            redisTemplate.delete(testKey);
            
            if (testValue.equals(result)) {
                return Result.success("Redis连接成功！读写测试通过");
            } else {
                return Result.error("Redis读写测试失败");
            }
        } catch (Exception e) {
            log.error("Redis连接失败", e);
            return Result.error("Redis连接失败: " + e.getMessage());
        }
    }

    /**
     * 创建测试用户 (Create)
     */
    @PostMapping("/user")
    public Result<User> createTestUser(@RequestBody CreateUserRequest request) {
        try {
            User user = new User();
            user.setUsername(request.getUsername());
            user.setPassword(request.getPassword());
            user.setRole(request.getRole() != null ? request.getRole() : "USER");
            
            int result = userMapper.insert(user);
            if (result > 0) {
                log.info("创建用户成功: {}", user);
                return Result.success(user);
            } else {
                return Result.error("创建用户失败");
            }
        } catch (Exception e) {
            log.error("创建用户失败", e);
            return Result.error("创建用户失败: " + e.getMessage());
        }
    }

    /**
     * 查询用户 (Read)
     */
    @GetMapping("/user/{id}")
    public Result<User> getUser(@PathVariable Long id) {
        try {
            User user = userMapper.selectById(id);
            if (user != null) {
                return Result.success(user);
            } else {
                return Result.error("用户不存在");
            }
        } catch (Exception e) {
            log.error("查询用户失败", e);
            return Result.error("查询用户失败: " + e.getMessage());
        }
    }

    /**
     * 查询所有用户
     */
    @GetMapping("/users")
    public Result<List<User>> getAllUsers() {
        try {
            List<User> users = userMapper.selectList(null);
            return Result.success(users);
        } catch (Exception e) {
            log.error("查询用户列表失败", e);
            return Result.error("查询用户列表失败: " + e.getMessage());
        }
    }

    /**
     * 更新用户 (Update)
     */
    @PutMapping("/user/{id}")
    public Result<User> updateUser(@PathVariable Long id, @RequestBody UpdateUserRequest request) {
        try {
            User user = userMapper.selectById(id);
            if (user == null) {
                return Result.error("用户不存在");
            }
            
            if (request.getUsername() != null) {
                user.setUsername(request.getUsername());
            }
            if (request.getPassword() != null) {
                user.setPassword(request.getPassword());
            }
            if (request.getRole() != null) {
                user.setRole(request.getRole());
            }
            
            int result = userMapper.updateById(user);
            if (result > 0) {
                log.info("更新用户成功: {}", user);
                return Result.success(user);
            } else {
                return Result.error("更新用户失败");
            }
        } catch (Exception e) {
            log.error("更新用户失败", e);
            return Result.error("更新用户失败: " + e.getMessage());
        }
    }

    /**
     * 删除用户 (Delete)
     */
    @DeleteMapping("/user/{id}")
    public Result<String> deleteUser(@PathVariable Long id) {
        try {
            User user = userMapper.selectById(id);
            if (user == null) {
                return Result.error("用户不存在");
            }
            
            int result = userMapper.deleteById(id);
            if (result > 0) {
                log.info("删除用户成功: ID={}", id);
                return Result.success("删除用户成功");
            } else {
                return Result.error("删除用户失败");
            }
        } catch (Exception e) {
            log.error("删除用户失败", e);
            return Result.error("删除用户失败: " + e.getMessage());
        }
    }

    /**
     * 创建用户请求DTO
     */
    public static class CreateUserRequest {
        private String username;
        private String password;
        private String role;

        // Getters and Setters
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        public String getPassword() { return password; }
        public void setPassword(String password) { this.password = password; }
        public String getRole() { return role; }
        public void setRole(String role) { this.role = role; }
    }

    /**
     * 更新用户请求DTO
     */
    public static class UpdateUserRequest {
        private String username;
        private String password;
        private String role;

        // Getters and Setters
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        public String getPassword() { return password; }
        public void setPassword(String password) { this.password = password; }
        public String getRole() { return role; }
        public void setRole(String role) { this.role = role; }
    }
}
