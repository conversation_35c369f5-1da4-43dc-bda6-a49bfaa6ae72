package com.healthdiet.user.controller;

import com.healthdiet.common.result.Result;
import com.healthdiet.user.dto.HealthProfileRequest;
import com.healthdiet.user.dto.HealthProfileResponse;
import com.healthdiet.user.service.UserHealthProfileService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 用户健康档案控制器
 */
@RestController
@RequestMapping("/health-profile")
@RequiredArgsConstructor
public class UserHealthProfileController {
    
    private final UserHealthProfileService userHealthProfileService;
    
    /**
     * 创建或更新健康档案
     */
    @PostMapping("/{userId}")
    public Result<Void> saveOrUpdateHealthProfile(
            @PathVariable Long userId,
            @Valid @RequestBody HealthProfileRequest request) {
        try {
            userHealthProfileService.saveOrUpdateHealthProfile(userId, request);
            return Result.success("健康档案保存成功", null);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 获取用户健康档案
     */
    @GetMapping("/{userId}")
    public Result<HealthProfileResponse> getHealthProfile(@PathVariable Long userId) {
        try {
            HealthProfileResponse response = userHealthProfileService.getHealthProfile(userId);
            if (response == null) {
                return Result.notFound();
            }
            return Result.success(response);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 删除用户健康档案
     */
    @DeleteMapping("/{userId}")
    public Result<Void> deleteHealthProfile(@PathVariable Long userId) {
        try {
            userHealthProfileService.deleteHealthProfile(userId);
            return Result.success("健康档案删除成功", null);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
}
