spring:
  application:
    name: auth-service-test
  
  # 使用内存数据库进行测试
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:testdb;MODE=MySQL;DATABASE_TO_LOWER=TRUE
    username: sa
    password: 
  
  # 禁用Nacos
  cloud:
    nacos:
      discovery:
        enabled: false
      config:
        enabled: false
  
  # 禁用Redis
  data:
    redis:
      host: localhost
      port: 6379
      password: 
      database: 0

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 日志配置
logging:
  level:
    com.healthdiet.auth: debug
