@echo off
echo ========================================
echo 健康饮食推荐平台 - 服务启动脚本
echo ========================================

echo.
echo 检查Java环境...
java -version
if %errorlevel% neq 0 (
    echo 错误: 未找到Java环境，请确保已安装JDK 17+
    pause
    exit /b 1
)

echo.
echo 检查Maven环境...
mvn -version
if %errorlevel% neq 0 (
    echo 错误: 未找到Maven环境，请确保已安装Maven
    pause
    exit /b 1
)

echo.
echo ========================================
echo 编译项目...
echo ========================================
mvn clean compile -f pom.xml
if %errorlevel% neq 0 (
    echo 错误: 项目编译失败
    pause
    exit /b 1
)

echo.
echo ========================================
echo 项目编译成功！
echo ========================================

echo.
echo 注意事项:
echo 1. 请确保MySQL服务已启动，并执行了sql/init.sql初始化脚本
echo 2. 请确保Redis服务已启动
echo 3. 请确保Nacos服务已启动 (可选，测试时可以禁用)
echo.

echo 服务端口分配:
echo - 网关服务: 8080
echo - 认证服务: 8081  
echo - 用户服务: 8082
echo - 菜谱服务: 8083
echo.

echo 要启动服务，请在新的命令行窗口中运行:
echo.
echo 启动认证服务:
echo cd auth-service ^&^& mvn spring-boot:run
echo.
echo 启动用户服务:
echo cd user-service ^&^& mvn spring-boot:run
echo.
echo 启动网关服务:
echo cd gateway-service ^&^& mvn spring-boot:run
echo.

echo API测试地址: http://localhost:8080
echo 详细测试说明请查看: test-api.md

pause
