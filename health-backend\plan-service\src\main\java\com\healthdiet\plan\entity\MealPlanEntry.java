package com.healthdiet.plan.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDate;

/**
 * 餐食计划条目实体类
 */
@Data
@TableName("meal_plan_entries")
public class MealPlanEntry {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 菜谱ID
     */
    private Long recipeId;
    
    /**
     * 计划日期
     */
    private LocalDate planDate;
    
    /**
     * 餐别类型：1-早餐，2-午餐，3-晚餐
     */
    private Integer mealType;
}
