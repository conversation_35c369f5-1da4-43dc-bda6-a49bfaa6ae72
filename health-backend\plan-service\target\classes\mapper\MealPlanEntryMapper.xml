<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.healthdiet.plan.mapper.MealPlanEntryMapper">

    <!-- 根据用户ID和日期范围查询餐食计划 -->
    <select id="selectByUserIdAndDateRange" resultType="com.healthdiet.plan.entity.MealPlanEntry">
        SELECT id, user_id, recipe_id, plan_date, meal_type
        FROM meal_plan_entries
        WHERE user_id = #{userId}
          AND plan_date BETWEEN #{startDate} AND #{endDate}
        ORDER BY plan_date, meal_type
    </select>

    <!-- 根据用户ID、日期和餐别查询餐食计划 -->
    <select id="selectByUserIdAndDateAndMealType" resultType="com.healthdiet.plan.entity.MealPlanEntry">
        SELECT id, user_id, recipe_id, plan_date, meal_type
        FROM meal_plan_entries
        WHERE user_id = #{userId}
          AND plan_date = #{planDate}
          AND meal_type = #{mealType}
        LIMIT 1
    </select>

</mapper>
