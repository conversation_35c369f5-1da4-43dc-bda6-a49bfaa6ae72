<template>
  <MainLayout>
    <div class="home-container">
      <!-- 欢迎区域 -->
      <div class="welcome-section">
        <el-card class="welcome-card">
          <div class="welcome-content">
            <div class="welcome-text">
              <h1>欢迎回来，{{ userStore.userInfo?.username }}！</h1>
              <p>今天也要健康饮食哦～</p>
            </div>
            <div class="welcome-stats">
              <div class="stat-item">
                <div class="stat-number">{{ userProfile?.targetCalories || 0 }}</div>
                <div class="stat-label">目标卡路里</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">{{ userProfile?.bmr || 0 }}</div>
                <div class="stat-label">基础代谢</div>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 快捷操作 -->
      <div class="quick-actions">
        <h2>快捷操作</h2>
        <el-row :gutter="24">
          <el-col :span="6">
            <el-card class="action-card" @click="$router.push('/recipes')">
              <div class="action-content">
                <el-icon size="32" color="#409eff"><Bowl /></el-icon>
                <h3>浏览菜谱</h3>
                <p>发现更多美味菜谱</p>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="action-card" @click="$router.push('/ai-recipe')">
              <div class="action-content">
                <el-icon size="32" color="#67c23a"><MagicStick /></el-icon>
                <h3>AI生成菜谱</h3>
                <p>智能推荐个性化菜谱</p>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="action-card" @click="$router.push('/meal-plan')">
              <div class="action-content">
                <el-icon size="32" color="#e6a23c"><Calendar /></el-icon>
                <h3>餐食计划</h3>
                <p>制定你的饮食计划</p>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="action-card" @click="$router.push('/favorites')">
              <div class="action-content">
                <el-icon size="32" color="#f56c6c"><Star /></el-icon>
                <h3>我的收藏</h3>
                <p>查看收藏的菜谱</p>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 推荐菜谱 -->
      <div class="recommended-recipes">
        <h2>推荐菜谱</h2>
        <el-row :gutter="24">
          <el-col :span="8" v-for="recipe in recommendedRecipes" :key="recipe.id">
            <el-card class="recipe-card" @click="$router.push(`/recipes/${recipe.id}`)">
              <div class="recipe-image">
                <img :src="recipe.coverImageUrl || '/default-recipe.jpg'" :alt="recipe.title" />
              </div>
              <div class="recipe-info">
                <h3>{{ recipe.title }}</h3>
                <p>{{ recipe.description }}</p>
                <div class="recipe-meta">
                  <span><el-icon><Timer /></el-icon> {{ recipe.cookingTime }}分钟</span>
                  <span><el-icon><Star /></el-icon> {{ recipe.estimatedCalories }}卡</span>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </div>
  </MainLayout>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import MainLayout from '@/components/Layout/MainLayout.vue'
import { useUserStore } from '@/stores/user'
import { getUserProfile } from '@/api/user'
import { searchRecipes } from '@/api/recipe'
import type { UserProfile } from '@/types/user'
import type { Recipe } from '@/types/recipe'

const userStore = useUserStore()
const userProfile = ref<UserProfile | null>(null)
const recommendedRecipes = ref<Recipe[]>([])

onMounted(async () => {
  try {
    // 获取用户健康档案
    const profile = await getUserProfile()
    userProfile.value = profile
    if (profile) {
      userStore.setUserProfile(profile)
    }
  } catch (error) {
    console.error('获取用户档案失败:', error)
  }

  try {
    // 获取推荐菜谱
    const result = await searchRecipes({ keyword: '', page: 0, size: 6 })
    recommendedRecipes.value = result.content
  } catch (error) {
    console.error('获取推荐菜谱失败:', error)
  }
})
</script>

<style scoped>
.home-container {
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-section {
  margin-bottom: 32px;
}

.welcome-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
}

.welcome-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-text h1 {
  font-size: 28px;
  margin-bottom: 8px;
}

.welcome-text p {
  font-size: 16px;
  opacity: 0.9;
}

.welcome-stats {
  display: flex;
  gap: 32px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.8;
}

.quick-actions {
  margin-bottom: 32px;
}

.quick-actions h2 {
  margin-bottom: 16px;
  color: #333;
}

.action-card {
  cursor: pointer;
  transition: all 0.3s;
  height: 120px;
}

.action-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.action-content {
  text-align: center;
  padding: 16px;
}

.action-content h3 {
  margin: 12px 0 8px;
  color: #333;
}

.action-content p {
  color: #666;
  font-size: 14px;
}

.recommended-recipes h2 {
  margin-bottom: 16px;
  color: #333;
}

.recipe-card {
  cursor: pointer;
  transition: all 0.3s;
  margin-bottom: 16px;
}

.recipe-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.recipe-image {
  height: 160px;
  overflow: hidden;
  border-radius: 8px;
  margin-bottom: 12px;
}

.recipe-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.recipe-info h3 {
  margin-bottom: 8px;
  color: #333;
  font-size: 16px;
}

.recipe-info p {
  color: #666;
  font-size: 14px;
  margin-bottom: 12px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.recipe-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #999;
}

.recipe-meta span {
  display: flex;
  align-items: center;
  gap: 4px;
}
</style>
