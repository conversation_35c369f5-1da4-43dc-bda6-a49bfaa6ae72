package com.healthdiet.aigc.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 菜谱生成请求DTO
 */
@Data
public class RecipeGenerationRequest {
    
    @NotBlank(message = "生成要求不能为空")
    @Size(max = 500, message = "生成要求长度不能超过500个字符")
    private String prompt;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 是否考虑用户健康目标
     */
    private Boolean considerHealthGoal = false;
}
