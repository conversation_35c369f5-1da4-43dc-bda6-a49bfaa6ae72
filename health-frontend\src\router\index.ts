import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/home'
    },
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/auth/LoginView.vue'),
      meta: { requiresGuest: true }
    },
    {
      path: '/register',
      name: 'Register',
      component: () => import('@/views/auth/RegisterView.vue'),
      meta: { requiresGuest: true }
    },
    {
      path: '/home',
      name: 'Home',
      component: () => import('@/views/HomeView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/recipes',
      name: 'Recipes',
      component: () => import('@/views/recipe/RecipeListView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/recipes/:id',
      name: 'RecipeDetail',
      component: () => import('@/views/recipe/RecipeDetailView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/recipes/create',
      name: 'CreateRecipe',
      component: () => import('@/views/recipe/CreateRecipeView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/ai-recipe',
      name: 'AiRecipe',
      component: () => import('@/views/recipe/AiRecipeView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/meal-plan',
      name: 'MealPlan',
      component: () => import('@/views/plan/MealPlanView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/favorites',
      name: 'Favorites',
      component: () => import('@/views/recipe/FavoritesView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/profile',
      name: 'Profile',
      component: () => import('@/views/user/ProfileView.vue'),
      meta: { requiresAuth: true }
    }
  ]
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore()

  if (to.meta.requiresAuth && !userStore.isLoggedIn) {
    next('/login')
  } else if (to.meta.requiresGuest && userStore.isLoggedIn) {
    next('/home')
  } else {
    next()
  }
})

export default router
