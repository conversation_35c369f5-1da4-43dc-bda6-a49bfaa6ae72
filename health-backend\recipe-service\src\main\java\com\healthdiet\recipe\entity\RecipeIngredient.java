package com.healthdiet.recipe.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * 菜谱食材实体类
 */
@Data
@TableName("recipe_ingredients")
public class RecipeIngredient {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 菜谱ID
     */
    private Long recipeId;
    
    /**
     * 食材名称
     */
    private String ingredientName;
    
    /**
     * 用量，如：200g, 1个, 适量
     */
    private String quantity;
}
