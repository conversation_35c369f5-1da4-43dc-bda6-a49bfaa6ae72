package com.healthdiet.aigc.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * DeepSeek API配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "deepseek.api")
public class DeepSeekConfig {
    
    private String key;
    private String baseUrl;
    private String model;
    private Long timeout;
    private Integer maxTokens;
}
