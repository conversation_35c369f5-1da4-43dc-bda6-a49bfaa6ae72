package com.healthdiet.aigc.service;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import com.healthdiet.aigc.dto.AiRecipeData;

/**
 * 菜谱服务Feign客户端
 */
@FeignClient(name = "recipe-service", url = "http://localhost:8083")
public interface RecipeServiceClient {

    /**
     * 创建AI生成的菜谱
     */
    @PostMapping("/api/recipes/ai")
    Long createAiRecipe(@RequestParam("userId") Long userId, @RequestBody AiRecipeData aiRecipeData);
}
