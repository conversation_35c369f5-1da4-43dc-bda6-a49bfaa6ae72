package com.healthdiet.aigc.dto;

import lombok.Data;

/**
 * 菜谱生成响应DTO
 */
@Data
public class RecipeGenerationResponse {
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 任务状态：PENDING-等待中, PROCESSING-处理中, COMPLETED-已完成, FAILED-失败
     */
    private String status;
    
    /**
     * 生成的菜谱ID（完成时返回）
     */
    private Long recipeId;
    
    /**
     * 错误信息（失败时返回）
     */
    private String errorMessage;
    
    /**
     * 创建时间
     */
    private java.time.LocalDateTime createdAt;
    
    /**
     * 完成时间
     */
    private java.time.LocalDateTime completedAt;
}
