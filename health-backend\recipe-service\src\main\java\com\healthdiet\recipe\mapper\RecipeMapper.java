package com.healthdiet.recipe.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.healthdiet.recipe.entity.Recipe;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 菜谱Mapper接口
 */
@Mapper
public interface RecipeMapper extends BaseMapper<Recipe> {
    
    /**
     * 分页搜索菜谱
     */
    IPage<Recipe> searchRecipes(Page<Recipe> page, @Param("keyword") String keyword, @Param("userId") Long userId);
    
    /**
     * 获取用户收藏的菜谱
     */
    IPage<Recipe> getFavoriteRecipes(Page<Recipe> page, @Param("userId") Long userId);
}
