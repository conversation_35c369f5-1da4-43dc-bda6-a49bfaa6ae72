<template>
  <div class="register-container">
    <!-- 左侧背景区域 -->
    <div class="register-left">
      <div class="brand-content">
        <div class="brand-logo">
          <div class="logo-icon">🥗</div>
        </div>
        <h1 class="brand-title">健康饮食推荐平台</h1>
        <p class="brand-subtitle">基于AIGC的智能化菜谱平台</p>
        <div class="feature-list">
          <div class="feature-item">
            <el-icon><MagicStick /></el-icon>
            <span>AI智能菜谱生成</span>
          </div>
          <div class="feature-item">
            <el-icon><Calendar /></el-icon>
            <span>个性化餐食计划</span>
          </div>
          <div class="feature-item">
            <el-icon><TrendCharts /></el-icon>
            <span>营养健康管理</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧注册表单 -->
    <div class="register-right">
      <div class="register-card">
        <div class="register-header">
          <h2>创建账号</h2>
          <p>加入健康饮食平台，开启健康生活</p>
        </div>
      
      <el-form
        ref="registerFormRef"
        :model="registerForm"
        :rules="registerRules"
        class="register-form"
        @submit.prevent="handleRegister"
      >
        <el-form-item prop="username">
          <el-input
            v-model="registerForm.username"
            placeholder="请输入用户名"
            size="large"
            clearable
            class="register-input"
          >
            <template #prefix>
              <el-icon class="input-icon"><User /></el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item prop="email">
          <el-input
            v-model="registerForm.email"
            placeholder="请输入邮箱"
            size="large"
            clearable
            class="register-input"
          >
            <template #prefix>
              <el-icon class="input-icon"><Message /></el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="registerForm.password"
            type="password"
            placeholder="请输入密码"
            size="large"
            clearable
            show-password
            class="register-input"
          >
            <template #prefix>
              <el-icon class="input-icon"><Lock /></el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item prop="confirmPassword">
          <el-input
            v-model="registerForm.confirmPassword"
            type="password"
            placeholder="请确认密码"
            size="large"
            clearable
            show-password
            class="register-input"
            @keyup.enter="handleRegister"
          >
            <template #prefix>
              <el-icon class="input-icon"><Lock /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            size="large"
            class="register-btn"
            :loading="loading"
            @click="handleRegister"
          >
            注册
          </el-button>
        </el-form-item>
      </el-form>
      
        <div class="register-footer">
          <span>已有账号？</span>
          <router-link to="/login" class="login-link">立即登录</router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { User, Lock, Message, MagicStick, Calendar, TrendCharts } from '@element-plus/icons-vue'
import { register } from '@/api/user'
import type { RegisterRequest } from '@/types/user'

const router = useRouter()

const registerFormRef = ref<FormInstance>()
const loading = ref(false)

const registerForm = reactive<RegisterRequest>({
  username: '',
  email: '',
  password: '',
  confirmPassword: ''
})

const validateConfirmPassword = (rule: any, value: any, callback: any) => {
  if (value !== registerForm.password) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

const registerRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在3到20个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' }
  ]
}

const handleRegister = async () => {
  if (!registerFormRef.value) return
  
  try {
    await registerFormRef.value.validate()
    loading.value = true
    
    await register(registerForm)
    
    ElMessage.success('注册成功，请登录')
    router.push('/login')
  } catch (error) {
    console.error('注册失败:', error)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.register-container {
  min-height: 100vh;
  display: flex;
  background: #f5f7fa;
  max-width: 1400px;
  margin: 0 auto;
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);
}

/* 左侧品牌展示区域 */
.register-left {
  flex: 1;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px;
  position: relative;
  overflow: hidden;
}

.register-left::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.brand-content {
  text-align: center;
  color: white;
  z-index: 1;
  position: relative;
  max-width: 500px;
}

.brand-logo {
  margin-bottom: 32px;
}

.logo-icon {
  font-size: 64px;
  margin-bottom: 16px;
  display: block;
}

.brand-title {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 16px;
  line-height: 1.2;
}

.brand-subtitle {
  font-size: 18px;
  opacity: 0.9;
  margin-bottom: 48px;
  line-height: 1.5;
}

.feature-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
  align-items: flex-start;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 16px;
  opacity: 0.9;
}

.feature-item .el-icon {
  font-size: 24px;
  color: rgba(255, 255, 255, 0.8);
}

/* 右侧注册表单区域 */
.register-right {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 40px;
  background: white;
}

.register-card {
  width: 100%;
  max-width: 400px;
}

.register-header {
  text-align: center;
  margin-bottom: 40px;
}

.register-header h2 {
  font-size: 28px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  line-height: 1.2;
}

.register-header p {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.register-form {
  margin-bottom: 24px;
}

.register-form .el-form-item {
  margin-bottom: 24px;
}

.register-input {
  height: 48px !important;
}

.register-input .el-input__wrapper {
  padding: 12px 16px !important;
  border-radius: 8px !important;
  border: 1px solid #dcdfe6 !important;
  box-shadow: none !important;
  background-color: #fff !important;
}

.register-input .el-input__wrapper:hover {
  border-color: #c0c4cc !important;
}

.register-input .el-input__wrapper.is-focus {
  border-color: #409eff !important;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2) !important;
}

.register-input .el-input__inner {
  height: 100% !important;
  line-height: 1.5 !important;
  font-size: 14px !important;
}

.input-icon {
  color: #a8abb2 !important;
  font-size: 16px !important;
}

.register-btn {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 8px;
  border: none;
  background: #409eff;
  color: white;
  cursor: pointer;
  transition: all 0.3s;
}

.register-btn:hover {
  background: #337ecc;
}

.register-btn:disabled {
  background: #a0cfff;
  cursor: not-allowed;
}

.register-footer {
  text-align: center;
  color: #666;
  font-size: 14px;
  margin: 0;
}

.login-link {
  color: #409eff;
  text-decoration: none;
  margin-left: 4px;
  font-weight: 500;
}

.login-link:hover {
  text-decoration: underline;
  color: #337ecc;
}

/* 大屏幕优化 */
@media (min-width: 1400px) {
  .register-container {
    max-width: 1600px;
  }

  .brand-title {
    font-size: 42px;
  }

  .brand-subtitle {
    font-size: 20px;
  }

  .register-right {
    padding: 80px 60px;
  }
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .register-container {
    flex-direction: column;
  }

  .register-left {
    flex: 0 0 300px;
    padding: 40px 20px;
  }

  .brand-title {
    font-size: 28px;
  }

  .brand-subtitle {
    font-size: 16px;
    margin-bottom: 32px;
  }

  .feature-list {
    flex-direction: row;
    justify-content: center;
    flex-wrap: wrap;
    gap: 16px;
  }

  .feature-item {
    flex-direction: column;
    text-align: center;
    gap: 8px;
    font-size: 14px;
  }

  .register-right {
    flex: 1;
    padding: 40px 20px;
  }
}

@media (max-width: 768px) {
  .register-left {
    flex: 0 0 250px;
    padding: 30px 20px;
  }

  .logo-icon {
    font-size: 48px;
  }

  .brand-title {
    font-size: 24px;
  }

  .brand-subtitle {
    font-size: 14px;
  }

  .register-right {
    padding: 30px 20px;
  }

  .register-header h2 {
    font-size: 24px;
  }
}

@media (max-width: 480px) {
  .register-container {
    min-height: 100vh;
  }

  .register-left {
    flex: 0 0 200px;
    padding: 20px;
  }

  .brand-content {
    max-width: 100%;
  }

  .logo-icon {
    font-size: 40px;
    margin-bottom: 12px;
  }

  .brand-title {
    font-size: 20px;
    margin-bottom: 12px;
  }

  .brand-subtitle {
    font-size: 13px;
    margin-bottom: 24px;
  }

  .feature-list {
    gap: 12px;
  }

  .feature-item {
    font-size: 12px;
  }

  .feature-item .el-icon {
    font-size: 18px;
  }

  .register-right {
    padding: 20px;
  }

  .register-card {
    max-width: 100%;
  }

  .register-header {
    margin-bottom: 30px;
  }

  .register-header h2 {
    font-size: 20px;
  }

  .register-header p {
    font-size: 13px;
  }
}
</style>
