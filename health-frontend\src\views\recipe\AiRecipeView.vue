<template>
  <MainLayout>
    <div class="ai-recipe-container">
      <el-card class="form-card">
        <template #header>
          <div class="card-header">
            <el-icon size="24" color="#409eff"><MagicStick /></el-icon>
            <h2>AI智能生成菜谱</h2>
          </div>
        </template>
        
        <el-form
          ref="aiFormRef"
          :model="aiForm"
          :rules="aiRules"
          label-width="120px"
        >
          <el-form-item label="口味偏好" prop="preferences">
            <el-input
              v-model="aiForm.preferences"
              type="textarea"
              :rows="3"
              placeholder="请描述您的口味偏好，例如：喜欢清淡、偏爱川菜、素食主义等"
            />
          </el-form-item>
          
          <el-form-item label="饮食限制">
            <el-input
              v-model="aiForm.dietaryRestrictions"
              type="textarea"
              :rows="2"
              placeholder="请描述饮食限制，例如：不吃辣、对海鲜过敏、糖尿病饮食等（可选）"
            />
          </el-form-item>
          
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="烹饪时间">
                <el-input-number
                  v-model="aiForm.cookingTime"
                  :min="10"
                  :max="180"
                  placeholder="分钟"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="份数">
                <el-input-number
                  v-model="aiForm.servings"
                  :min="1"
                  :max="10"
                  placeholder="人份"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-form-item>
            <el-button
              type="primary"
              size="large"
              @click="handleGenerate"
              :loading="generating"
              style="width: 200px"
            >
              <el-icon><MagicStick /></el-icon>
              生成菜谱
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 生成进度 -->
      <el-card v-if="taskId" class="progress-card">
        <template #header>
          <h3>生成进度</h3>
        </template>
        <div class="progress-content">
          <el-progress
            :percentage="progressPercentage"
            :status="progressStatus"
            :stroke-width="8"
          />
          <p class="progress-text">{{ progressText }}</p>
          <el-button v-if="taskStatus === 'COMPLETED'" type="primary" @click="viewGeneratedRecipe">
            查看生成的菜谱
          </el-button>
        </div>
      </el-card>

      <!-- 历史生成记录 -->
      <el-card class="history-card">
        <template #header>
          <h3>最近生成的菜谱</h3>
        </template>
        <div class="history-list">
          <el-empty v-if="historyRecipes.length === 0" description="暂无生成记录" />
          <el-row v-else :gutter="24">
            <el-col :span="8" v-for="recipe in historyRecipes" :key="recipe.id">
              <el-card class="recipe-card" @click="viewRecipe(recipe.id)">
                <div class="recipe-image">
                  <img :src="recipe.coverImageUrl || '/default-recipe.jpg'" :alt="recipe.title" />
                  <div class="ai-badge">AI生成</div>
                </div>
                <div class="recipe-info">
                  <h4>{{ recipe.title }}</h4>
                  <p>{{ recipe.description }}</p>
                  <div class="recipe-meta">
                    <span><el-icon><Timer /></el-icon> {{ recipe.cookingTime }}分钟</span>
                    <span><el-icon><Star /></el-icon> {{ recipe.estimatedCalories }}卡</span>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </el-card>
    </div>
  </MainLayout>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import MainLayout from '@/components/Layout/MainLayout.vue'
import { generateAiRecipe, getAiTaskStatus, searchRecipes } from '@/api/recipe'
import type { AiRecipeRequest, Recipe } from '@/types/recipe'

const router = useRouter()

const aiFormRef = ref<FormInstance>()
const generating = ref(false)
const taskId = ref('')
const taskStatus = ref('')
const progressPercentage = ref(0)
const progressStatus = ref<'success' | 'exception' | 'warning' | ''>('')
const progressText = ref('')
const historyRecipes = ref<Recipe[]>([])

let pollTimer: number | null = null

const aiForm = reactive<AiRecipeRequest>({
  preferences: '',
  dietaryRestrictions: '',
  cookingTime: 30,
  servings: 2
})

const aiRules: FormRules = {
  preferences: [
    { required: true, message: '请描述您的口味偏好', trigger: 'blur' },
    { min: 10, message: '请详细描述您的偏好，至少10个字符', trigger: 'blur' }
  ]
}

const handleGenerate = async () => {
  if (!aiFormRef.value) return
  
  try {
    await aiFormRef.value.validate()
    generating.value = true
    
    taskId.value = await generateAiRecipe(aiForm)
    taskStatus.value = 'PENDING'
    progressPercentage.value = 10
    progressText.value = '正在分析您的需求...'
    
    // 开始轮询任务状态
    startPolling()
    
    ElMessage.success('AI正在为您生成菜谱，请稍候...')
  } catch (error) {
    console.error('生成菜谱失败:', error)
  } finally {
    generating.value = false
  }
}

const startPolling = () => {
  if (pollTimer) {
    clearInterval(pollTimer)
  }
  
  pollTimer = setInterval(async () => {
    try {
      const result = await getAiTaskStatus(taskId.value)
      taskStatus.value = String(result.status)

      switch (String(result.status)) {
        case 'PENDING':
          progressPercentage.value = 20
          progressText.value = '正在生成菜谱内容...'
          break
        case 'PROCESSING':
          progressPercentage.value = 60
          progressText.value = '正在优化菜谱结构...'
          break
        case 'COMPLETED':
          progressPercentage.value = 100
          progressStatus.value = 'success'
          progressText.value = '菜谱生成完成！'
          stopPolling()
          loadHistoryRecipes()
          ElMessage.success('AI菜谱生成成功！')
          break
        case 'FAILED':
          progressPercentage.value = 100
          progressStatus.value = 'exception'
          progressText.value = '生成失败，请重试'
          stopPolling()
          ElMessage.error('菜谱生成失败：' + (result.error || '未知错误'))
          break
      }
    } catch (error) {
      console.error('获取任务状态失败:', error)
      stopPolling()
    }
  }, 2000)
}

const stopPolling = () => {
  if (pollTimer) {
    clearInterval(pollTimer)
    pollTimer = null
  }
}

const viewGeneratedRecipe = () => {
  // 这里应该从任务结果中获取生成的菜谱ID
  // 暂时跳转到菜谱列表页面
  router.push('/recipes')
}

const viewRecipe = (id: number) => {
  router.push(`/recipes/${id}`)
}

const loadHistoryRecipes = async () => {
  try {
    // 获取AI生成的菜谱
    const result = await searchRecipes({ keyword: '', page: 0, size: 6 })
    historyRecipes.value = result.content.filter((recipe: any) => recipe.isAiGenerated)
  } catch (error) {
    console.error('加载历史记录失败:', error)
  }
}

onMounted(() => {
  loadHistoryRecipes()
})

onUnmounted(() => {
  stopPolling()
})
</script>

<style scoped>
.ai-recipe-container {
  max-width: 1000px;
  margin: 0 auto;
}

.form-card {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.card-header h2 {
  margin: 0;
  color: #333;
}

.progress-card {
  margin-bottom: 24px;
}

.progress-content {
  text-align: center;
}

.progress-text {
  margin: 16px 0;
  color: #666;
  font-size: 14px;
}

.history-card {
  margin-bottom: 24px;
}

.history-list {
  margin-top: 16px;
}

.recipe-card {
  cursor: pointer;
  transition: all 0.3s;
  margin-bottom: 16px;
}

.recipe-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.recipe-image {
  position: relative;
  height: 160px;
  overflow: hidden;
  border-radius: 8px;
  margin-bottom: 12px;
}

.recipe-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.ai-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background: linear-gradient(45deg, #409eff, #67c23a);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.recipe-info h4 {
  margin-bottom: 8px;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.recipe-info p {
  color: #666;
  font-size: 14px;
  margin-bottom: 12px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.recipe-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #999;
}

.recipe-meta span {
  display: flex;
  align-items: center;
  gap: 4px;
}
</style>
