package com.healthdiet.recipe.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 菜谱列表响应DTO
 */
@Data
public class RecipeListResponse {
    
    private Long id;
    private Long userId;
    private String username;
    private String title;
    private String description;
    private String coverImageUrl;
    private Integer estimatedCalories;
    private Boolean isAiGenerated;
    private Boolean isFavorited;
    private LocalDateTime createdAt;
}
