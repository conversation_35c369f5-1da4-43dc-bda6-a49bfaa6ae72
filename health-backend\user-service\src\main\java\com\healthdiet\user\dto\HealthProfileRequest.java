package com.healthdiet.user.dto;

import lombok.Data;

import jakarta.validation.constraints.*;
import java.math.BigDecimal;

/**
 * 健康档案请求DTO
 */
@Data
public class HealthProfileRequest {
    
    @NotNull(message = "性别不能为空")
    @Min(value = 0, message = "性别值无效")
    @Max(value = 1, message = "性别值无效")
    private Integer gender;
    
    @NotNull(message = "年龄不能为空")
    @Min(value = 1, message = "年龄必须大于0")
    @Max(value = 120, message = "年龄不能超过120")
    private Integer age;
    
    @NotNull(message = "身高不能为空")
    @DecimalMin(value = "50.0", message = "身高不能小于50cm")
    @DecimalMax(value = "250.0", message = "身高不能超过250cm")
    private BigDecimal height;
    
    @NotNull(message = "体重不能为空")
    @DecimalMin(value = "20.0", message = "体重不能小于20kg")
    @DecimalMax(value = "300.0", message = "体重不能超过300kg")
    private BigDecimal weight;
    
    @NotNull(message = "运动频率不能为空")
    @Min(value = 1, message = "运动频率值无效")
    @Max(value = 4, message = "运动频率值无效")
    private Integer activityLevel;
    
    @NotNull(message = "健康目标不能为空")
    @Min(value = 1, message = "健康目标值无效")
    @Max(value = 3, message = "健康目标值无效")
    private Integer goal;
}
