package com.healthdiet.plan.dto;

import lombok.Data;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 周餐食计划响应DTO
 */
@Data
public class WeeklyMealPlanResponse {
    
    /**
     * 周开始日期
     */
    private LocalDate weekStartDate;
    
    /**
     * 周结束日期
     */
    private LocalDate weekEndDate;
    
    /**
     * 每日餐食计划
     * Key: 日期字符串 (yyyy-MM-dd)
     * Value: 该日期的餐食计划列表
     */
    private Map<String, DailyMealPlan> dailyPlans;
    
    /**
     * 每日餐食计划
     */
    @Data
    public static class DailyMealPlan {
        /**
         * 日期
         */
        private LocalDate date;
        
        /**
         * 早餐
         */
        private MealPlanResponse breakfast;
        
        /**
         * 午餐
         */
        private MealPlanResponse lunch;
        
        /**
         * 晚餐
         */
        private MealPlanResponse dinner;
        
        /**
         * 当日总热量
         */
        private Integer totalCalories;
    }
}
