package com.healthdiet.recipe.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.healthdiet.recipe.dto.RecipeRequest;
import com.healthdiet.recipe.entity.Ingredient;
import com.healthdiet.recipe.entity.RecipeIngredient;
import com.healthdiet.recipe.mapper.IngredientMapper;
import com.healthdiet.recipe.mapper.RecipeIngredientMapper;
import com.healthdiet.recipe.service.NutritionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 营养计算服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class NutritionServiceImpl implements NutritionService {
    
    private final IngredientMapper ingredientMapper;
    private final RecipeIngredientMapper recipeIngredientMapper;
    
    // 重量单位转换映射
    private static final Map<String, Double> UNIT_CONVERSION = new HashMap<>();
    
    static {
        // 重量单位转换为克
        UNIT_CONVERSION.put("kg", 1000.0);
        UNIT_CONVERSION.put("公斤", 1000.0);
        UNIT_CONVERSION.put("斤", 500.0);
        UNIT_CONVERSION.put("g", 1.0);
        UNIT_CONVERSION.put("克", 1.0);
        UNIT_CONVERSION.put("ml", 1.0); // 液体按1ml=1g计算
        UNIT_CONVERSION.put("毫升", 1.0);
        UNIT_CONVERSION.put("l", 1000.0);
        UNIT_CONVERSION.put("升", 1000.0);
        
        // 常见食材的估算重量（个、勺、块等）
        UNIT_CONVERSION.put("个", 50.0); // 平均一个的重量
        UNIT_CONVERSION.put("勺", 15.0); // 一勺的重量
        UNIT_CONVERSION.put("块", 30.0); // 一块的重量
        UNIT_CONVERSION.put("片", 10.0); // 一片的重量
        UNIT_CONVERSION.put("根", 20.0); // 一根的重量
        UNIT_CONVERSION.put("颗", 5.0);  // 一颗的重量
    }
    
    @Override
    public int calculateRecipeCalories(Long recipeId) {
        // 获取菜谱的所有食材
        LambdaQueryWrapper<RecipeIngredient> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RecipeIngredient::getRecipeId, recipeId);
        List<RecipeIngredient> recipeIngredients = recipeIngredientMapper.selectList(queryWrapper);
        
        int totalCalories = 0;
        
        for (RecipeIngredient recipeIngredient : recipeIngredients) {
            int calories = calculateIngredientCalories(
                recipeIngredient.getIngredientName(), 
                recipeIngredient.getQuantity()
            );
            totalCalories += calories;
        }
        
        return totalCalories;
    }
    
    @Override
    public int calculateCaloriesFromIngredients(List<RecipeRequest.IngredientItem> ingredients) {
        int totalCalories = 0;
        
        for (RecipeRequest.IngredientItem ingredient : ingredients) {
            int calories = calculateIngredientCalories(ingredient.getName(), ingredient.getQuantity());
            totalCalories += calories;
        }
        
        return totalCalories;
    }
    
    @Override
    public void addIngredientNutrition(String name, Integer caloriesPer100g) {
        // 检查是否已存在
        LambdaQueryWrapper<Ingredient> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Ingredient::getName, name);
        Ingredient existingIngredient = ingredientMapper.selectOne(queryWrapper);
        
        if (existingIngredient != null) {
            // 更新现有记录
            existingIngredient.setCaloriesPer100g(caloriesPer100g);
            ingredientMapper.updateById(existingIngredient);
        } else {
            // 创建新记录
            Ingredient ingredient = new Ingredient();
            ingredient.setName(name);
            ingredient.setCaloriesPer100g(caloriesPer100g);
            ingredientMapper.insert(ingredient);
        }
    }
    
    @Override
    public void importNutritionData() {
        // 批量导入常见食材的营养数据
        Map<String, Integer> nutritionData = getCommonIngredientsNutrition();
        
        for (Map.Entry<String, Integer> entry : nutritionData.entrySet()) {
            addIngredientNutrition(entry.getKey(), entry.getValue());
        }
        
        log.info("成功导入 {} 种食材的营养数据", nutritionData.size());
    }
    
    /**
     * 计算单个食材的热量
     */
    private int calculateIngredientCalories(String ingredientName, String quantity) {
        // 查找食材营养信息
        LambdaQueryWrapper<Ingredient> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Ingredient::getName, ingredientName);
        Ingredient ingredient = ingredientMapper.selectOne(queryWrapper);
        
        if (ingredient == null || ingredient.getCaloriesPer100g() == null) {
            log.warn("未找到食材 {} 的营养信息", ingredientName);
            return 0;
        }
        
        // 解析重量
        double weightInGrams = parseWeight(quantity);
        if (weightInGrams <= 0) {
            log.warn("无法解析食材 {} 的重量: {}", ingredientName, quantity);
            return 0;
        }
        
        // 计算热量
        double calories = (weightInGrams / 100.0) * ingredient.getCaloriesPer100g();
        return (int) Math.round(calories);
    }
    
    /**
     * 解析重量字符串，返回克数
     */
    private double parseWeight(String quantity) {
        if (quantity == null || quantity.trim().isEmpty()) {
            return 0;
        }
        
        quantity = quantity.trim().toLowerCase();
        
        // 处理"适量"、"少许"等模糊描述
        if (quantity.contains("适量") || quantity.contains("少许")) {
            return 10.0; // 默认10克
        }
        if (quantity.contains("一点") || quantity.contains("一些")) {
            return 5.0; // 默认5克
        }
        
        // 使用正则表达式提取数字和单位
        Pattern pattern = Pattern.compile("([0-9.]+)\\s*([a-zA-Z\\u4e00-\\u9fa5]*)");
        Matcher matcher = pattern.matcher(quantity);
        
        if (matcher.find()) {
            try {
                double number = Double.parseDouble(matcher.group(1));
                String unit = matcher.group(2);
                
                if (unit.isEmpty()) {
                    // 没有单位，默认为克
                    return number;
                }
                
                // 查找单位转换
                Double conversionFactor = UNIT_CONVERSION.get(unit);
                if (conversionFactor != null) {
                    return number * conversionFactor;
                }
                
                // 尝试模糊匹配
                for (Map.Entry<String, Double> entry : UNIT_CONVERSION.entrySet()) {
                    if (unit.contains(entry.getKey()) || entry.getKey().contains(unit)) {
                        return number * entry.getValue();
                    }
                }
                
                log.warn("未知的重量单位: {}", unit);
                return number; // 默认为克
                
            } catch (NumberFormatException e) {
                log.warn("无法解析数字: {}", matcher.group(1));
                return 0;
            }
        }
        
        return 0;
    }
    
    /**
     * 获取常见食材营养数据
     */
    private Map<String, Integer> getCommonIngredientsNutrition() {
        Map<String, Integer> nutritionData = new HashMap<>();
        
        // 肉类
        nutritionData.put("鸡胸肉", 165);
        nutritionData.put("鸡腿肉", 181);
        nutritionData.put("牛肉", 250);
        nutritionData.put("猪肉", 242);
        nutritionData.put("羊肉", 203);
        nutritionData.put("鱼肉", 206);
        nutritionData.put("虾", 106);
        
        // 蛋类
        nutritionData.put("鸡蛋", 155);
        nutritionData.put("鸭蛋", 185);
        nutritionData.put("鹌鹑蛋", 158);
        
        // 主食
        nutritionData.put("大米", 130);
        nutritionData.put("面条", 137);
        nutritionData.put("面包", 265);
        nutritionData.put("馒头", 221);
        nutritionData.put("土豆", 76);
        nutritionData.put("红薯", 99);
        
        // 蔬菜
        nutritionData.put("西兰花", 34);
        nutritionData.put("胡萝卜", 41);
        nutritionData.put("白菜", 17);
        nutritionData.put("番茄", 18);
        nutritionData.put("黄瓜", 15);
        nutritionData.put("菠菜", 28);
        nutritionData.put("芹菜", 20);
        nutritionData.put("洋葱", 39);
        nutritionData.put("青椒", 22);
        
        // 水果
        nutritionData.put("苹果", 52);
        nutritionData.put("香蕉", 89);
        nutritionData.put("橙子", 43);
        nutritionData.put("葡萄", 69);
        nutritionData.put("草莓", 30);
        
        // 奶制品
        nutritionData.put("牛奶", 54);
        nutritionData.put("酸奶", 59);
        nutritionData.put("奶酪", 328);
        
        // 豆制品
        nutritionData.put("豆腐", 76);
        nutritionData.put("豆浆", 14);
        nutritionData.put("黄豆", 359);
        
        // 调料
        nutritionData.put("花生油", 899);
        nutritionData.put("橄榄油", 884);
        nutritionData.put("盐", 0);
        nutritionData.put("生抽", 63);
        nutritionData.put("老抽", 65);
        nutritionData.put("料酒", 103);
        nutritionData.put("白糖", 387);
        nutritionData.put("蜂蜜", 304);
        
        return nutritionData;
    }
}
