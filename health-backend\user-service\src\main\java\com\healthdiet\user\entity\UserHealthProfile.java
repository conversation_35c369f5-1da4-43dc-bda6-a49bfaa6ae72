package com.healthdiet.user.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户健康档案实体类
 */
@Data
@TableName("user_health_profiles")
public class UserHealthProfile {

    /**
     * 档案ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 身高(cm)
     */
    @TableField("height")
    private BigDecimal height;

    /**
     * 体重(kg)
     */
    @TableField("weight")
    private BigDecimal weight;

    /**
     * 年龄
     */
    @TableField("age")
    private Integer age;

    /**
     * 性别 (M/F)
     */
    @TableField("gender")
    private String gender;

    /**
     * 活动水平
     */
    @TableField("activity_level")
    private String activityLevel;

    /**
     * 健康目标
     */
    @TableField("health_goals")
    private String healthGoals;

    /**
     * 过敏信息
     */
    @TableField("allergies")
    private String allergies;

    /**
     * 饮食偏好
     */
    @TableField("dietary_preferences")
    private String dietaryPreferences;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}
