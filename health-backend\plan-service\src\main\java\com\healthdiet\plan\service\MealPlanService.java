package com.healthdiet.plan.service;

import com.healthdiet.plan.dto.*;

import java.time.LocalDate;

/**
 * 餐食计划服务接口
 */
public interface MealPlanService {
    
    /**
     * 添加餐食计划
     */
    MealPlanResponse addMealPlan(Long userId, MealPlanRequest request);
    
    /**
     * 删除餐食计划
     */
    void removeMealPlan(Long userId, Long planId);
    
    /**
     * 获取周餐食计划
     */
    WeeklyMealPlanResponse getWeeklyMealPlan(Long userId, LocalDate weekStartDate);
    
    /**
     * 生成购物清单
     */
    ShoppingListResponse generateShoppingList(Long userId, ShoppingListRequest request);
}
