<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.healthdiet.admin.mapper.AdminMapper">

    <!-- 根据用户名查询管理员 -->
    <select id="selectByUsername" resultType="com.healthdiet.admin.entity.Admin">
        SELECT id, username, password, real_name, email, role, status, created_at, updated_at
        FROM admins
        WHERE username = #{username}
        LIMIT 1
    </select>

</mapper>
