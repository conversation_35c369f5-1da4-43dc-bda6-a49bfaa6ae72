<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.healthdiet.recipe.mapper.RecipeMapper">

    <!-- 分页搜索菜谱 -->
    <select id="searchRecipes" resultType="com.healthdiet.recipe.entity.Recipe">
        SELECT r.*
        FROM recipes r
        <where>
            <if test="keyword != null and keyword != ''">
                AND r.title LIKE CONCAT('%', #{keyword}, '%')
            </if>
            <if test="userId != null">
                AND r.user_id = #{userId}
            </if>
        </where>
        ORDER BY r.created_at DESC
    </select>

    <!-- 获取用户收藏的菜谱 -->
    <select id="getFavoriteRecipes" resultType="com.healthdiet.recipe.entity.Recipe">
        SELECT r.*
        FROM recipes r
        INNER JOIN favorites f ON r.id = f.recipe_id
        WHERE f.user_id = #{userId}
        ORDER BY f.created_at DESC
    </select>

</mapper>
