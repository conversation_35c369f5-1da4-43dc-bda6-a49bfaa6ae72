@echo off
echo ========================================
echo 健康饮食推荐平台 - 服务健康检查
echo ========================================

echo.
echo 检查服务状态...

echo.
echo [1/3] 检查认证服务 (端口8081)...
curl -s -o nul -w "HTTP状态码: %%{http_code}" http://localhost:8081/actuator/health
if %errorlevel% equ 0 (
    echo  - 认证服务运行正常
) else (
    echo  - 认证服务未响应
)

echo.
echo [2/3] 检查用户服务 (端口8082)...
curl -s -o nul -w "HTTP状态码: %%{http_code}" http://localhost:8082/actuator/health
if %errorlevel% equ 0 (
    echo  - 用户服务运行正常
) else (
    echo  - 用户服务未响应
)

echo.
echo [3/3] 检查网关服务 (端口8080)...
curl -s -o nul -w "HTTP状态码: %%{http_code}" http://localhost:8080/actuator/health
if %errorlevel% equ 0 (
    echo  - 网关服务运行正常
) else (
    echo  - 网关服务未响应
)

echo.
echo ========================================
echo 健康检查完成
echo ========================================

echo.
echo 如果服务未响应，请检查:
echo 1. 服务是否已启动
echo 2. 端口是否被占用
echo 3. 数据库连接是否正常
echo 4. 配置文件是否正确

pause
