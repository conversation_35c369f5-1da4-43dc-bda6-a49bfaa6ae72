<template>
  <MainLayout>
    <div class="favorites-container">
      <div class="page-header">
        <h2>我的收藏</h2>
        <p>{{ total }}个收藏的菜谱</p>
      </div>

      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="8" animated />
      </div>

      <div v-else-if="favorites.length === 0" class="empty-container">
        <el-empty description="还没有收藏任何菜谱">
          <el-button type="primary" @click="$router.push('/recipes')">
            去浏览菜谱
          </el-button>
        </el-empty>
      </div>

      <div v-else>
        <div class="favorites-grid">
          <el-row :gutter="24">
            <el-col :span="8" v-for="recipe in favorites" :key="recipe.id">
              <el-card class="recipe-card" @click="viewRecipe(recipe.id)">
                <div class="recipe-image">
                  <img :src="recipe.coverImageUrl || '/default-recipe.jpg'" :alt="recipe.title" />
                  <div class="recipe-overlay">
                    <el-button
                      type="danger"
                      circle
                      @click.stop="unfavoriteRecipe(recipe)"
                      :icon="StarFilled"
                    />
                  </div>
                  <div v-if="recipe.isAiGenerated" class="ai-badge">AI生成</div>
                </div>
                <div class="recipe-info">
                  <h3>{{ recipe.title }}</h3>
                  <p>{{ recipe.description }}</p>
                  <div class="recipe-meta">
                    <span><el-icon><Timer /></el-icon> {{ recipe.cookingTime }}分钟</span>
                    <span><el-icon><Star /></el-icon> {{ recipe.estimatedCalories }}卡</span>
                    <span class="difficulty">{{ getDifficultyText(recipe.difficulty) }}</span>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>

        <div class="pagination-section" v-if="total > 0">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[12, 24, 48]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
  </MainLayout>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { StarFilled } from '@element-plus/icons-vue'
import MainLayout from '@/components/Layout/MainLayout.vue'
import { getMyFavorites, unfavoriteRecipe as unfavoriteRecipeApi } from '@/api/recipe'
import type { Recipe } from '@/types/recipe'

const router = useRouter()

const loading = ref(true)
const favorites = ref<Recipe[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(12)

const loadFavorites = async () => {
  try {
    loading.value = true
    const result = await getMyFavorites(currentPage.value - 1, pageSize.value)
    favorites.value = result.content
    total.value = result.totalElements
  } catch (error) {
    console.error('加载收藏失败:', error)
  } finally {
    loading.value = false
  }
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  loadFavorites()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadFavorites()
}

const viewRecipe = (id: number) => {
  router.push(`/recipes/${id}`)
}

const unfavoriteRecipe = async (recipe: Recipe) => {
  try {
    await ElMessageBox.confirm('确定要取消收藏这个菜谱吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await unfavoriteRecipeApi(recipe.id)
    ElMessage.success('已取消收藏')
    
    // 从列表中移除
    const index = favorites.value.findIndex(item => item.id === recipe.id)
    if (index > -1) {
      favorites.value.splice(index, 1)
      total.value--
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消收藏失败:', error)
    }
  }
}

const getDifficultyText = (difficulty: number) => {
  const map = { 1: '简单', 2: '中等', 3: '困难' }
  return map[difficulty as keyof typeof map] || '未知'
}

onMounted(() => {
  loadFavorites()
})
</script>

<style scoped>
.favorites-container {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 32px;
  text-align: center;
}

.page-header h2 {
  margin-bottom: 8px;
  color: #333;
}

.page-header p {
  color: #666;
  font-size: 14px;
}

.loading-container,
.empty-container {
  padding: 40px 0;
}

.favorites-grid {
  margin-bottom: 24px;
}

.recipe-card {
  cursor: pointer;
  transition: all 0.3s;
  margin-bottom: 24px;
  height: 320px;
}

.recipe-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.recipe-image {
  position: relative;
  height: 180px;
  overflow: hidden;
  border-radius: 8px;
  margin-bottom: 12px;
}

.recipe-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.recipe-overlay {
  position: absolute;
  top: 8px;
  right: 8px;
  opacity: 0;
  transition: opacity 0.3s;
}

.recipe-card:hover .recipe-overlay {
  opacity: 1;
}

.ai-badge {
  position: absolute;
  top: 8px;
  left: 8px;
  background: linear-gradient(45deg, #409eff, #67c23a);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.recipe-info h3 {
  margin-bottom: 8px;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.recipe-info p {
  color: #666;
  font-size: 14px;
  margin-bottom: 12px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.recipe-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #999;
}

.recipe-meta span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.difficulty {
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 4px;
}

.pagination-section {
  display: flex;
  justify-content: center;
  margin-top: 32px;
}
</style>
