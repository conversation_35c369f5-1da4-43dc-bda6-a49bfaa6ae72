<template>
  <MainLayout>
    <div class="profile-container">
      <el-row :gutter="32">
        <el-col :span="8">
          <el-card class="profile-card">
            <template #header>
              <h3>个人信息</h3>
            </template>
            <div class="profile-info">
              <div class="avatar-section">
                <el-avatar :size="80" :src="userStore.userInfo?.avatar">
                  {{ userStore.userInfo?.username?.charAt(0).toUpperCase() }}
                </el-avatar>
                <el-upload
                  class="avatar-uploader"
                  :show-file-list="false"
                  :on-success="handleAvatarSuccess"
                  :before-upload="beforeAvatarUpload"
                  action="/api/users/avatar"
                >
                  <el-button type="text" size="small">更换头像</el-button>
                </el-upload>
              </div>
              
              <div class="user-details">
                <div class="detail-item">
                  <label>用户名：</label>
                  <span>{{ userStore.userInfo?.username }}</span>
                </div>
                <div class="detail-item">
                  <label>邮箱：</label>
                  <span>{{ userStore.userInfo?.email }}</span>
                </div>
                <div class="detail-item">
                  <label>注册时间：</label>
                  <span>{{ formatDate(userStore.userInfo?.createdAt) }}</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="16">
          <el-card class="health-profile-card">
            <template #header>
              <div class="card-header">
                <h3>健康档案</h3>
                <el-button v-if="!isEditing" type="primary" @click="startEdit">编辑</el-button>
                <div v-else>
                  <el-button @click="cancelEdit">取消</el-button>
                  <el-button type="primary" @click="saveProfile" :loading="saving">保存</el-button>
                </div>
              </div>
            </template>
            
            <div v-if="!isEditing && userProfile" class="profile-display">
              <el-row :gutter="24">
                <el-col :span="12">
                  <div class="info-item">
                    <label>性别：</label>
                    <span>{{ userProfile.gender === 1 ? '男' : '女' }}</span>
                  </div>
                  <div class="info-item">
                    <label>年龄：</label>
                    <span>{{ userProfile.age }}岁</span>
                  </div>
                  <div class="info-item">
                    <label>身高：</label>
                    <span>{{ userProfile.height }}cm</span>
                  </div>
                  <div class="info-item">
                    <label>体重：</label>
                    <span>{{ userProfile.weight }}kg</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="info-item">
                    <label>运动频率：</label>
                    <span>{{ getActivityLevelText(userProfile.activityLevel) }}</span>
                  </div>
                  <div class="info-item">
                    <label>健康目标：</label>
                    <span>{{ getHealthGoalText(userProfile.healthGoal) }}</span>
                  </div>
                  <div class="info-item">
                    <label>基础代谢：</label>
                    <span>{{ userProfile.bmr }}卡/天</span>
                  </div>
                  <div class="info-item">
                    <label>目标卡路里：</label>
                    <span>{{ userProfile.targetCalories }}卡/天</span>
                  </div>
                </el-col>
              </el-row>
            </div>
            
            <div v-else-if="isEditing" class="profile-edit">
              <el-form
                ref="profileFormRef"
                :model="profileForm"
                :rules="profileRules"
                label-width="100px"
              >
                <el-row :gutter="24">
                  <el-col :span="12">
                    <el-form-item label="性别" prop="gender">
                      <el-radio-group v-model="profileForm.gender">
                        <el-radio :label="1">男</el-radio>
                        <el-radio :label="2">女</el-radio>
                      </el-radio-group>
                    </el-form-item>
                    <el-form-item label="年龄" prop="age">
                      <el-input-number
                        v-model="profileForm.age"
                        :min="1"
                        :max="120"
                        style="width: 100%"
                      />
                    </el-form-item>
                    <el-form-item label="身高(cm)" prop="height">
                      <el-input-number
                        v-model="profileForm.height"
                        :min="100"
                        :max="250"
                        style="width: 100%"
                      />
                    </el-form-item>
                    <el-form-item label="体重(kg)" prop="weight">
                      <el-input-number
                        v-model="profileForm.weight"
                        :min="30"
                        :max="200"
                        :precision="1"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="运动频率" prop="activityLevel">
                      <el-select v-model="profileForm.activityLevel" style="width: 100%">
                        <el-option label="久坐不动" :value="1" />
                        <el-option label="轻度活动" :value="2" />
                        <el-option label="中度活动" :value="3" />
                        <el-option label="重度活动" :value="4" />
                        <el-option label="极重度活动" :value="5" />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="健康目标" prop="healthGoal">
                      <el-select v-model="profileForm.healthGoal" style="width: 100%">
                        <el-option label="减重" :value="1" />
                        <el-option label="增重" :value="2" />
                        <el-option label="保持" :value="3" />
                        <el-option label="增肌" :value="4" />
                        <el-option label="塑形" :value="5" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
            
            <div v-else class="no-profile">
              <el-empty description="还没有设置健康档案">
                <el-button type="primary" @click="startEdit">立即设置</el-button>
              </el-empty>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </MainLayout>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, type FormInstance, type FormRules, type UploadProps } from 'element-plus'
import MainLayout from '@/components/Layout/MainLayout.vue'
import { useUserStore } from '@/stores/user'
import { getUserProfile, saveUserProfile, uploadAvatar } from '@/api/user'
import type { UserProfile, UserProfileRequest } from '@/types/user'

const userStore = useUserStore()

const profileFormRef = ref<FormInstance>()
const isEditing = ref(false)
const saving = ref(false)
const userProfile = ref<UserProfile | null>(null)

const profileForm = reactive<UserProfileRequest>({
  gender: 1,
  age: 25,
  height: 170,
  weight: 60,
  activityLevel: 2,
  healthGoal: 3
})

const profileRules: FormRules = {
  gender: [{ required: true, message: '请选择性别', trigger: 'change' }],
  age: [{ required: true, message: '请输入年龄', trigger: 'blur' }],
  height: [{ required: true, message: '请输入身高', trigger: 'blur' }],
  weight: [{ required: true, message: '请输入体重', trigger: 'blur' }],
  activityLevel: [{ required: true, message: '请选择运动频率', trigger: 'change' }],
  healthGoal: [{ required: true, message: '请选择健康目标', trigger: 'change' }]
}

const loadProfile = async () => {
  try {
    const profile = await getUserProfile()
    userProfile.value = profile
    if (profile) {
      userStore.setUserProfile(profile)
    }
  } catch (error) {
    console.error('加载健康档案失败:', error)
    userProfile.value = null
  }
}

const startEdit = () => {
  if (userProfile.value) {
    Object.assign(profileForm, {
      gender: userProfile.value.gender,
      age: userProfile.value.age,
      height: userProfile.value.height,
      weight: userProfile.value.weight,
      activityLevel: userProfile.value.activityLevel,
      healthGoal: userProfile.value.healthGoal
    })
  }
  isEditing.value = true
}

const cancelEdit = () => {
  isEditing.value = false
}

const saveProfile = async () => {
  if (!profileFormRef.value) return
  
  try {
    await profileFormRef.value.validate()
    saving.value = true
    
    const savedProfile = await saveUserProfile(profileForm)
    userProfile.value = savedProfile
    if (savedProfile) {
      userStore.setUserProfile(savedProfile)
    }
    
    ElMessage.success('健康档案保存成功')
    isEditing.value = false
  } catch (error) {
    console.error('保存健康档案失败:', error)
  } finally {
    saving.value = false
  }
}

const handleAvatarSuccess: UploadProps['onSuccess'] = (response) => {
  if (userStore.userInfo) {
    userStore.userInfo.avatar = response.data
  }
  ElMessage.success('头像更新成功')
}

const beforeAvatarUpload: UploadProps['beforeUpload'] = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

const getActivityLevelText = (level: number) => {
  const map = {
    1: '久坐不动',
    2: '轻度活动',
    3: '中度活动',
    4: '重度活动',
    5: '极重度活动'
  }
  return map[level as keyof typeof map] || '未知'
}

const getHealthGoalText = (goal: number) => {
  const map = {
    1: '减重',
    2: '增重',
    3: '保持',
    4: '增肌',
    5: '塑形'
  }
  return map[goal as keyof typeof map] || '未知'
}

const formatDate = (dateStr?: string) => {
  if (!dateStr) return '未知'
  return new Date(dateStr).toLocaleDateString()
}

onMounted(() => {
  loadProfile()
})
</script>

<style scoped>
.profile-container {
  max-width: 1200px;
  margin: 0 auto;
}

.profile-card,
.health-profile-card {
  height: fit-content;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
}

.profile-info {
  text-align: center;
}

.avatar-section {
  margin-bottom: 24px;
}

.avatar-uploader {
  margin-top: 12px;
}

.user-details {
  text-align: left;
}

.detail-item,
.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.detail-item label,
.info-item label {
  font-weight: 500;
  color: #666;
}

.detail-item span,
.info-item span {
  color: #333;
}

.profile-display {
  padding: 16px 0;
}

.profile-edit {
  padding: 16px 0;
}

.no-profile {
  padding: 40px 0;
  text-align: center;
}
</style>
