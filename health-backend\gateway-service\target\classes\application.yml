server:
  port: 8080

spring:
  application:
    name: gateway-service
  config:
    import: "optional:nacos:gateway-service.yml"
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
      config:
        server-addr: localhost:8848
        file-extension: yml
    gateway:
      discovery:
        locator:
          enabled: true
          lower-case-service-id: true
      routes:
        - id: auth-service
          uri: lb://auth-service
          predicates:
            - Path=/auth/**
        - id: user-service
          uri: lb://user-service
          predicates:
            - Path=/users/**
        - id: recipe-service
          uri: lb://recipe-service
          predicates:
            - Path=/recipes/**
        - id: aigc-service
          uri: lb://aigc-service
          predicates:
            - Path=/aigc/**
        - id: plan-service
          uri: lb://plan-service
          predicates:
            - Path=/plans/**
        - id: admin-service
          uri: lb://admin-service
          predicates:
            - Path=/admin/**
          filters:
            - StripPrefix=2

        # 计划服务路由
        - id: plan-service
          uri: lb://plan-service
          predicates:
            - Path=/api/plans/**
          filters:
            - StripPrefix=2

        # 管理服务路由
        - id: admin-service
          uri: lb://admin-service
          predicates:
            - Path=/api/admin/**
          filters:
            - StripPrefix=2
      
      # 全局CORS配置
      globalcors:
        cors-configurations:
          '[/**]':
            allowedOriginPatterns: "*"
            allowedMethods: "*"
            allowedHeaders: "*"
            allowCredentials: true

# Sentinel配置
management:
  endpoints:
    web:
      exposure:
        include: "*"

logging:
  level:
    com.healthdiet.gateway: debug
    org.springframework.cloud.gateway: debug
