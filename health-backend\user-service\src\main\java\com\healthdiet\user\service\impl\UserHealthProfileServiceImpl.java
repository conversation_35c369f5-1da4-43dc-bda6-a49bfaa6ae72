package com.healthdiet.user.service.impl;

import com.healthdiet.user.dto.HealthProfileRequest;
import com.healthdiet.user.dto.HealthProfileResponse;
import com.healthdiet.user.entity.UserHealthProfile;
import com.healthdiet.user.mapper.UserHealthProfileMapper;
import com.healthdiet.user.service.UserHealthProfileService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * 用户健康档案服务实现类
 */
@Service
@RequiredArgsConstructor
public class UserHealthProfileServiceImpl implements UserHealthProfileService {
    
    private final UserHealthProfileMapper userHealthProfileMapper;
    
    @Override
    public void saveOrUpdateHealthProfile(Long userId, HealthProfileRequest request) {
        UserHealthProfile profile = new UserHealthProfile();
        BeanUtils.copyProperties(request, profile);
        profile.setUserId(userId);
        
        // 注意：目标卡路里计算可以在业务逻辑中处理，不存储在数据库中
        
        // 检查是否已存在档案
        UserHealthProfile existingProfile = userHealthProfileMapper.selectById(userId);
        if (existingProfile != null) {
            // 更新
            userHealthProfileMapper.updateById(profile);
        } else {
            // 新增
            userHealthProfileMapper.insert(profile);
        }
    }
    
    @Override
    public HealthProfileResponse getHealthProfile(Long userId) {
        UserHealthProfile profile = userHealthProfileMapper.selectById(userId);
        if (profile == null) {
            return null;
        }
        
        HealthProfileResponse response = new HealthProfileResponse();
        BeanUtils.copyProperties(profile, response);
        
        // 设置文本描述
        response.setGenderText();
        response.setActivityLevelText();
        response.setGoalText();
        
        // 计算BMI
        response.calculateBmi();
        
        return response;
    }
    
    @Override
    public void deleteHealthProfile(Long userId) {
        userHealthProfileMapper.deleteById(userId);
    }
    
    /**
     * 计算目标卡路里
     * 使用Mifflin-St Jeor公式
     */
    private int calculateTargetCalories(HealthProfileRequest request) {
        // 计算BMR (基础代谢率)
        double bmr;
        if (request.getGender() == 1) { // 男性
            bmr = 10 * request.getWeight().doubleValue() + 
                  6.25 * request.getHeight().doubleValue() - 
                  5 * request.getAge() + 5;
        } else { // 女性
            bmr = 10 * request.getWeight().doubleValue() + 
                  6.25 * request.getHeight().doubleValue() - 
                  5 * request.getAge() - 161;
        }
        
        // 根据运动频率计算活动系数
        double activityFactor;
        switch (request.getActivityLevel()) {
            case 1 -> activityFactor = 1.2;  // 基本不动
            case 2 -> activityFactor = 1.375; // 每周1-3次
            case 3 -> activityFactor = 1.55;  // 每周3-5次
            case 4 -> activityFactor = 1.725; // 每周6-7次
            default -> activityFactor = 1.2;
        }
        
        // 计算TDEE (每日总消耗热量)
        double tdee = bmr * activityFactor;
        
        // 根据健康目标调整
        switch (request.getGoal()) {
            case 1 -> tdee -= 400; // 减脂：减少400卡路里
            case 3 -> tdee += 400; // 增肌：增加400卡路里
            // case 2: 保持体重，不调整
        }
        
        return (int) Math.round(tdee);
    }
}
