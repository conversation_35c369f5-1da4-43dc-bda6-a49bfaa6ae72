package com.healthdiet.plan.controller;

import com.healthdiet.plan.dto.*;
import com.healthdiet.plan.service.MealPlanService;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.time.LocalDate;

/**
 * 餐食计划控制器
 */
@RestController
@RequestMapping("/api/meal-plans")
@RequiredArgsConstructor
public class MealPlanController {
    
    private final MealPlanService mealPlanService;
    
    /**
     * 添加餐食计划
     */
    @PostMapping
    public ResponseEntity<MealPlanResponse> addMealPlan(
            @RequestHeader("X-User-Id") Long userId,
            @Valid @RequestBody MealPlanRequest request) {
        
        MealPlanResponse response = mealPlanService.addMealPlan(userId, request);
        return ResponseEntity.ok(response);
    }
    
    /**
     * 删除餐食计划
     */
    @DeleteMapping("/{planId}")
    public ResponseEntity<Void> removeMealPlan(
            @RequestHeader("X-User-Id") Long userId,
            @PathVariable Long planId) {
        
        mealPlanService.removeMealPlan(userId, planId);
        return ResponseEntity.ok().build();
    }
    
    /**
     * 获取周餐食计划
     */
    @GetMapping("/weekly")
    public ResponseEntity<WeeklyMealPlanResponse> getWeeklyMealPlan(
            @RequestHeader("X-User-Id") Long userId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate weekStartDate) {
        
        WeeklyMealPlanResponse response = mealPlanService.getWeeklyMealPlan(userId, weekStartDate);
        return ResponseEntity.ok(response);
    }
    
    /**
     * 生成购物清单
     */
    @PostMapping("/shopping-list")
    public ResponseEntity<ShoppingListResponse> generateShoppingList(
            @RequestHeader("X-User-Id") Long userId,
            @Valid @RequestBody ShoppingListRequest request) {
        
        ShoppingListResponse response = mealPlanService.generateShoppingList(userId, request);
        return ResponseEntity.ok(response);
    }
}
