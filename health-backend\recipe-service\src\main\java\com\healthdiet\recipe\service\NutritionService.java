package com.healthdiet.recipe.service;

import com.healthdiet.recipe.dto.RecipeRequest;

/**
 * 营养计算服务接口
 */
public interface NutritionService {
    
    /**
     * 计算菜谱的估算热量
     */
    int calculateRecipeCalories(Long recipeId);
    
    /**
     * 根据食材列表计算热量
     */
    int calculateCaloriesFromIngredients(java.util.List<RecipeRequest.IngredientItem> ingredients);
    
    /**
     * 添加食材营养信息
     */
    void addIngredientNutrition(String name, Integer caloriesPer100g);
    
    /**
     * 批量导入食材营养数据
     */
    void importNutritionData();
}
