import request from './request'
import type { 
  MealPlan, 
  WeeklyMealPlan, 
  MealPlanRequest, 
  ShoppingList, 
  ShoppingListRequest 
} from '@/types/plan'

// 添加餐食计划
export function addMealPlan(data: MealPlanRequest) {
  return request.post<MealPlan>('/plans/meal-plans', data)
}

// 删除餐食计划
export function removeMealPlan(planId: number) {
  return request.delete(`/plans/meal-plans/${planId}`)
}

// 获取周餐食计划
export function getWeeklyMealPlan(weekStartDate: string) {
  return request.get<WeeklyMealPlan>('/plans/meal-plans/weekly', {
    params: { weekStartDate }
  })
}

// 生成购物清单
export function generateShoppingList(data: ShoppingListRequest) {
  return request.post<ShoppingList>('/plans/meal-plans/shopping-list', data)
}
