package com.healthdiet.recipe;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * 菜谱服务启动类
 */
@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients
@MapperScan("com.healthdiet.recipe.mapper")
public class RecipeApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(RecipeApplication.class, args);
    }
}
