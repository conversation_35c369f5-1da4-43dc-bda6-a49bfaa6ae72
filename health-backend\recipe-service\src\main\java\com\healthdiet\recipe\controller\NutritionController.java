package com.healthdiet.recipe.controller;

import com.healthdiet.common.result.Result;
import com.healthdiet.recipe.service.NutritionService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 营养管理控制器
 */
@RestController
@RequestMapping("/nutrition")
@RequiredArgsConstructor
public class NutritionController {
    
    private final NutritionService nutritionService;
    
    /**
     * 计算菜谱热量
     */
    @GetMapping("/recipe/{recipeId}/calories")
    public Result<Integer> calculateRecipeCalories(@PathVariable Long recipeId) {
        try {
            int calories = nutritionService.calculateRecipeCalories(recipeId);
            return Result.success(calories);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 添加食材营养信息
     */
    @PostMapping("/ingredient")
    public Result<Void> addIngredientNutrition(
            @RequestParam String name,
            @RequestParam Integer caloriesPer100g) {
        try {
            nutritionService.addIngredientNutrition(name, caloriesPer100g);
            return Result.success("食材营养信息添加成功", null);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 批量导入营养数据
     */
    @PostMapping("/import")
    public Result<Void> importNutritionData() {
        try {
            nutritionService.importNutritionData();
            return Result.success("营养数据导入成功", null);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
}
