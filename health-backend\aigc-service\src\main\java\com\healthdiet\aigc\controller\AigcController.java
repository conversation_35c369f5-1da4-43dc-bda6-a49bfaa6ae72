package com.healthdiet.aigc.controller;

import com.healthdiet.aigc.dto.RecipeGenerationRequest;
import com.healthdiet.aigc.dto.RecipeGenerationResponse;
import com.healthdiet.aigc.service.AigcService;
import com.healthdiet.common.result.Result;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * AIGC控制器
 */
@RestController
@RequestMapping("/aigc")
@RequiredArgsConstructor
public class AigcController {
    
    private final AigcService aigcService;
    
    /**
     * 提交菜谱生成任务
     */
    @PostMapping("/generate")
    public Result<RecipeGenerationResponse> generateRecipe(
            @RequestHeader("X-User-Id") Long userId,
            @Valid @RequestBody RecipeGenerationRequest request) {
        try {
            request.setUserId(userId);
            RecipeGenerationResponse response = aigcService.submitRecipeGeneration(request);
            return Result.success("任务提交成功", response);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 查询任务状态
     */
    @GetMapping("/task/{taskId}")
    public Result<RecipeGenerationResponse> getTaskStatus(@PathVariable String taskId) {
        try {
            RecipeGenerationResponse response = aigcService.getTaskStatus(taskId);
            return Result.success(response);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 取消任务
     */
    @DeleteMapping("/task/{taskId}")
    public Result<Void> cancelTask(@PathVariable String taskId) {
        try {
            aigcService.cancelTask(taskId);
            return Result.success("任务已取消", null);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
}
