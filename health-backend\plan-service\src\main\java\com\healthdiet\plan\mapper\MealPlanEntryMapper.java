package com.healthdiet.plan.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.healthdiet.plan.entity.MealPlanEntry;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * 餐食计划Mapper接口
 */
@Mapper
public interface MealPlanEntryMapper extends BaseMapper<MealPlanEntry> {
    
    /**
     * 根据用户ID和日期范围查询餐食计划
     */
    List<MealPlanEntry> selectByUserIdAndDateRange(
            @Param("userId") Long userId,
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate
    );
    
    /**
     * 根据用户ID、日期和餐别查询餐食计划
     */
    MealPlanEntry selectByUserIdAndDateAndMealType(
            @Param("userId") Long userId,
            @Param("planDate") LocalDate planDate,
            @Param("mealType") Integer mealType
    );
}
