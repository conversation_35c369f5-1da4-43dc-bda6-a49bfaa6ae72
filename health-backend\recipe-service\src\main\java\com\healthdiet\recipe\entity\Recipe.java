package com.healthdiet.recipe.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 菜谱实体类
 */
@Data
@TableName("recipes")
public class Recipe {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 发布者用户ID
     */
    private Long userId;
    
    /**
     * 菜谱名称
     */
    private String title;
    
    /**
     * 菜谱简介
     */
    private String description;
    
    /**
     * 封面图片URL
     */
    private String coverImageUrl;
    
    /**
     * 估算热量
     */
    private Integer estimatedCalories;
    
    /**
     * 是否AI生成
     */
    private Boolean isAiGenerated;
    
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}
