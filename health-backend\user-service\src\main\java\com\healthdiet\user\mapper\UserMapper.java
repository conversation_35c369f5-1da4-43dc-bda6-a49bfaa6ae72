package com.healthdiet.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.healthdiet.user.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * 用户Mapper接口
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {
    
    /**
     * 根据用户名查询用户
     */
    @Select("SELECT * FROM users WHERE username = #{username}")
    User selectByUsername(String username);
    
    /**
     * 统计用户总数
     */
    @Select("SELECT COUNT(*) FROM users")
    Long countUsers();
    
    /**
     * 统计今日新增用户数
     */
    @Select("SELECT COUNT(*) FROM users WHERE DATE(created_at) = CURDATE()")
    Long countTodayNewUsers();
}
