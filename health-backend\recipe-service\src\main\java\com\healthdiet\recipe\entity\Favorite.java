package com.healthdiet.recipe.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 收藏实体类
 */
@Data
@TableName("favorites")
public class Favorite {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 菜谱ID
     */
    private Long recipeId;
    
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
}
