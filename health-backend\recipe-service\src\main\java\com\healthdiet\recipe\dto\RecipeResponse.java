package com.healthdiet.recipe.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 菜谱响应DTO
 */
@Data
public class RecipeResponse {
    
    private Long id;
    private Long userId;
    private String username;
    private String title;
    private String description;
    private String coverImageUrl;
    private Integer estimatedCalories;
    private Boolean isAiGenerated;
    private Boolean isFavorited;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    private List<IngredientItem> ingredients;
    private List<StepItem> steps;
    
    @Data
    public static class IngredientItem {
        private Long id;
        private String name;
        private String quantity;
    }
    
    @Data
    public static class StepItem {
        private Long id;
        private Integer stepNumber;
        private String description;
        private String imageUrl;
    }
}
